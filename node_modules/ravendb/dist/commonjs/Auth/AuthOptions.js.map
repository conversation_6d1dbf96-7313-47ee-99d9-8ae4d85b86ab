{"version": 3, "file": "AuthOptions.js", "sourceRoot": "", "sources": ["../../../src/Auth/AuthOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import { CertificateType } from \"./Certificate.js\";\r\n\r\nexport interface IAuthOptions {\r\n    type?: CertificateType;\r\n    certificate?: string | Buffer;\r\n    password?: string;\r\n    ca?: string | Buffer;\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\r\nexport interface IStoreAuthOptions extends IAuthOptions {\r\n\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\r\nexport interface IRequestAuthOptions extends IAuthOptions {\r\n\r\n}\r\n"]}