{"version": 3, "file": "Certificate.js", "sourceRoot": "", "sources": ["../../../src/Auth/Certificate.ts"], "names": [], "mappings": ";;;AACA,4DAAsD;AACtD,qDAAoD;AAapD,MAAsB,WAAW;IACtB,MAAM,CAAU,GAAG,GAAoB,KAAK,CAAC;IAC7C,MAAM,CAAU,GAAG,GAAoB,KAAK,CAAC;IAE1C,YAAY,CAAkB;IAC9B,GAAG,CAAkB;IACrB,WAAW,CAAU;IAExB,MAAM,CAAC,iBAAiB,CAAC,OAAqB;QACjD,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,WAAW,GAAiB,IAAI,CAAC;QAErC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACvB,IAAA,qBAAU,EAAC,0BAA0B,EAAE,4BAA4B,CAAC,CAAC;QACzE,CAAC;QAED,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;gBACnB,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;gBAChF,MAAM;YACV,CAAC;YACD,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;gBACnB,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;gBAChF,MAAM;YACV,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACN,IAAA,qBAAU,EAAC,0BAA0B,EAAE,gCAAgC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5F,CAAC;QACL,CAAC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAEM,MAAM,CAAC,SAAS,CAAC,WAA4B,EAAE,UAAmB,EAAE,EAAoB;QAC3F,OAAO,IAAI,cAAc,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;IAC3D,CAAC;IAEM,MAAM,CAAC,SAAS,CAAC,WAA4B,EAAE,UAAmB,EAAE,EAAoB;QAC3F,OAAO,IAAI,cAAc,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED,YAAsB,WAA4B,EAAE,UAAmB,EAAE,EAAoB;QACzF,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;IAClB,CAAC;IAEM,cAAc;QACjB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO;gBACH,OAAO,EAAE;oBACL,UAAU,EAAE,IAAI,CAAC,WAAW;iBAC/B;aACJ,CAAA;QACL,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,eAAe;QACX,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO;gBACH,UAAU,EAAE,IAAI,CAAC,WAAW;aAC/B,CAAA;QACL,CAAC;QACD,OAAO,EAAE,CAAC;IACd,CAAC;IAEM,kBAAkB;QACrB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;QAC5C,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;;AA7EL,kCA8EC;AAED,MAAa,cAAe,SAAQ,WAAW;IAC1B,UAAU,GAAW,aAAa,CAAC;IACnC,SAAS,GAAW,iBAAiB,CAAC;IAC7C,IAAI,CAAS;IAEvB,YAAY,WAA4B,EAAE,UAAmB,EAAE,EAAoB;QAC/E,KAAK,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;QAEnC,IAAI,WAAW,YAAY,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,QAAQ,EAAE,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAErD,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACnC,IAAA,qBAAU,EAAC,0BAA0B,EAAE,mCAAmC,CAAC,CAAC;QAChF,CAAC;IACL,CAAC;IAEM,cAAc;QACjB,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;QACpD,OAAO;YACH,GAAG,IAAI;YACP,OAAO,EAAE;gBACL,GAAG,OAAO;gBACV,IAAI,EAAE,IAAI,CAAC,YAAY;gBACvB,GAAG,EAAE,IAAI,CAAC,IAAI;gBACd,EAAE,EAAE,IAAI,CAAC,GAAG;aACf;SACJ,CAAA;IACL,CAAC;IAEM,eAAe;QAClB,MAAM,OAAO,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;QACxC,OAAO;YACH,GAAG,OAAO;YACV,IAAI,EAAE,IAAI,CAAC,YAAY;YACvB,GAAG,EAAE,IAAI,CAAC,IAAI;YACd,EAAE,EAAE,IAAI,CAAC,GAAG;SACf,CAAA;IACL,CAAC;IAEM,kBAAkB;QACrB,MAAM,MAAM,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAC1C,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;YACzB,IAAI,EAAE,IAAI,CAAC,YAAY;YACvB,GAAG,EAAE,IAAI,CAAC,IAAI;YACd,EAAE,EAAE,IAAI,CAAC,GAAG;SACf,CAAC,CAAC;IACP,CAAC;IAES,UAAU,CAAC,KAAa;QAC9B,MAAM,IAAI,GAAW,IAAI,CAAC,YAAsB,CAAC;QACjD,MAAM,YAAY,GAAW,OAAO,CAAC;QACrC,MAAM,WAAW,GAAW,GAAG,YAAY,SAAS,KAAK,GAAG,YAAY,EAAE,CAAC;QAC3E,MAAM,SAAS,GAAW,GAAG,YAAY,OAAO,KAAK,GAAG,YAAY,EAAE,CAAC;QAEvE,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,GAAW,IAAI,CAAC,SAAS,CAC/B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EACzB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,MAAM,CAC7C,CAAC;YAEF,IAAI,CAAC,0BAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAvED,wCAuEC;AAED,MAAa,cAAe,SAAQ,WAAW;IAC3C,YAAY,WAA4B,EAAE,UAAmB,EAAE,EAAoB;QAC/E,IAAI,CAAC,CAAC,WAAW,YAAY,MAAM,CAAC,EAAE,CAAC;YACnC,IAAA,qBAAU,EAAC,0BAA0B,EAAE,oCAAoC,CAAC,CAAC;QACjF,CAAC;QAED,KAAK,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;IACvC,CAAC;IAEM,cAAc;QACjB,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;QACpD,OAAO;YACH,GAAG,IAAI;YACP,OAAO,EAAE;gBACL,GAAG,OAAO;gBACV,GAAG,EAAE,IAAI,CAAC,YAAY;gBACtB,EAAE,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;aAC5B;SACJ,CAAA;IACL,CAAC;IAED,eAAe;QACX,MAAM,OAAO,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;QACxC,OAAO;YACH,GAAG,OAAO;YACV,GAAG,EAAE,IAAI,CAAC,YAAY;YACtB,EAAE,EAAE,IAAI,CAAC,GAAG;SACf,CAAA;IACL,CAAC;IAEM,kBAAkB;QACrB,MAAM,MAAM,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAC1C,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;YACzB,GAAG,EAAE,IAAI,CAAC,YAAsB;YAChC,EAAE,EAAE,IAAI,CAAC,GAAG;SACf,CAAC,CAAC;IACP,CAAC;CACJ;AArCD,wCAqCC", "sourcesContent": ["import { IAuthOptions } from \"./AuthOptions.js\";\r\nimport { StringUtil } from \"../Utility/StringUtil.js\";\r\nimport { throwError } from \"../Exceptions/index.js\";\r\nimport { ClientOptions } from \"ws\";\r\nimport { Agent } from \"undici-types\";\r\nimport { ConnectionOptions } from \"node:tls\";\r\n\r\nexport type CertificateType = \"pem\" | \"pfx\";\r\n\r\nexport interface ICertificate {\r\n    toAgentOptions(): Agent.Options;\r\n    toSocketOptions(): ConnectionOptions;\r\n    toWebSocketOptions(): ClientOptions;\r\n}\r\n\r\nexport abstract class Certificate implements ICertificate {\r\n    public static readonly PEM: CertificateType = \"pem\";\r\n    public static readonly PFX: CertificateType = \"pfx\";\r\n\r\n    protected _certificate: string | Buffer;\r\n    protected _ca: string | Buffer;\r\n    protected _passphrase?: string;\r\n\r\n    public static createFromOptions(options: IAuthOptions): ICertificate {\r\n        if (!options) {\r\n            return null;\r\n        }\r\n\r\n        let certificate: ICertificate = null;\r\n\r\n        if (!options.certificate) {\r\n            throwError(\"InvalidArgumentException\", \"Certificate cannot be null\");\r\n        }\r\n\r\n        switch (options.type) {\r\n            case Certificate.PEM: {\r\n                certificate = this.createPem(options.certificate, options.password, options.ca);\r\n                break;\r\n            }\r\n            case Certificate.PFX: {\r\n                certificate = this.createPfx(options.certificate, options.password, options.ca);\r\n                break;\r\n            }\r\n            default: {\r\n                throwError(\"InvalidArgumentException\", \"Unsupported authOptions type: \" + options.type);\r\n            }\r\n        }\r\n\r\n        return certificate;\r\n    }\r\n\r\n    public static createPem(certificate: string | Buffer, passphrase?: string, ca?: string | Buffer) {\r\n        return new PemCertificate(certificate, passphrase, ca);\r\n    }\r\n\r\n    public static createPfx(certificate: string | Buffer, passphrase?: string, ca?: string | Buffer) {\r\n        return new PfxCertificate(certificate, passphrase, ca);\r\n    }\r\n\r\n    protected constructor(certificate: string | Buffer, passphrase?: string, ca?: string | Buffer) {\r\n        this._certificate = certificate;\r\n        this._passphrase = passphrase;\r\n        this._ca = ca;\r\n    }\r\n\r\n    public toAgentOptions(): Agent.Options {\r\n        if (this._passphrase) {\r\n            return {\r\n                connect: {\r\n                    passphrase: this._passphrase\r\n                }\r\n            }\r\n        }\r\n\r\n        return {};\r\n    }\r\n\r\n    toSocketOptions(): ConnectionOptions {\r\n        if (this._passphrase) {\r\n            return {\r\n                passphrase: this._passphrase\r\n            }\r\n        }\r\n        return {};\r\n    }\r\n\r\n    public toWebSocketOptions(): ClientOptions {\r\n        if (this._passphrase) {\r\n            return { passphrase: this._passphrase };\r\n        }\r\n\r\n        return {};\r\n    }\r\n}\r\n\r\nexport class PemCertificate extends Certificate {\r\n    private readonly _certToken: string = \"CERTIFICATE\";\r\n    private readonly _keyToken: string = \"RSA PRIVATE KEY\";\r\n    protected _key: string;\r\n\r\n    constructor(certificate: string | Buffer, passphrase?: string, ca?: string | Buffer) {\r\n        super(certificate, passphrase, ca);\r\n\r\n        if (certificate instanceof Buffer) {\r\n            this._certificate = certificate.toString();\r\n        }\r\n\r\n        this._key = this._fetchPart(this._keyToken);\r\n        this._certificate = this._fetchPart(this._certToken);\r\n\r\n        if (!this._key && !this._certificate) {\r\n            throwError(\"InvalidArgumentException\", \"Invalid .pem certificate provided\");\r\n        }\r\n    }\r\n\r\n    public toAgentOptions(): Agent.Options {\r\n        const { connect, ...rest } = super.toAgentOptions();\r\n        return {\r\n            ...rest,\r\n            connect: {\r\n                ...connect,\r\n                cert: this._certificate,\r\n                key: this._key,\r\n                ca: this._ca\r\n            }\r\n        }\r\n    }\r\n\r\n    public toSocketOptions(): ConnectionOptions {\r\n        const options = super.toSocketOptions();\r\n        return {\r\n            ...options,\r\n            cert: this._certificate,\r\n            key: this._key,\r\n            ca: this._ca\r\n        }\r\n    }\r\n\r\n    public toWebSocketOptions(): ClientOptions {\r\n        const result = super.toWebSocketOptions();\r\n        return Object.assign(result, {\r\n            cert: this._certificate,\r\n            key: this._key,\r\n            ca: this._ca\r\n        });\r\n    }\r\n\r\n    protected _fetchPart(token: string): string {\r\n        const cert: string = this._certificate as string;\r\n        const prefixSuffix: string = \"-----\";\r\n        const beginMarker: string = `${prefixSuffix}BEGIN ${token}${prefixSuffix}`;\r\n        const endMarker: string = `${prefixSuffix}END ${token}${prefixSuffix}`;\r\n\r\n        if (cert.includes(beginMarker) && cert.includes(endMarker)) {\r\n            const part: string = cert.substring(\r\n                cert.indexOf(beginMarker),\r\n                cert.indexOf(endMarker) + endMarker.length\r\n            );\r\n\r\n            if (!StringUtil.isNullOrWhitespace(part)) {\r\n                return part;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n}\r\n\r\nexport class PfxCertificate extends Certificate {\r\n    constructor(certificate: string | Buffer, passphrase?: string, ca?: string | Buffer) {\r\n        if (!(certificate instanceof Buffer)) {\r\n            throwError(\"InvalidArgumentException\", \"Pfx certificate should be a Buffer\");\r\n        }\r\n\r\n        super(certificate, passphrase, ca);\r\n    }\r\n\r\n    public toAgentOptions(): Agent.Options {\r\n        const { connect, ...rest } = super.toAgentOptions();\r\n        return {\r\n            ...rest,\r\n            connect: {\r\n                ...connect,\r\n                pfx: this._certificate,\r\n                ca: this._ca ?? undefined\r\n            }\r\n        }\r\n    }\r\n\r\n    toSocketOptions(): ConnectionOptions {\r\n        const options = super.toSocketOptions();\r\n        return {\r\n            ...options,\r\n            pfx: this._certificate,\r\n            ca: this._ca\r\n        }\r\n    }\r\n\r\n    public toWebSocketOptions(): ClientOptions {\r\n        const result = super.toWebSocketOptions();\r\n        return Object.assign(result, {\r\n            pfx: this._certificate as Buffer,\r\n            ca: this._ca\r\n        });\r\n    }\r\n}\r\n"]}