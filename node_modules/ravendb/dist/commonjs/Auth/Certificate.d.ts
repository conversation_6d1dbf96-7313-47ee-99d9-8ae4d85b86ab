import { IAuthOptions } from "./AuthOptions.js";
import { ClientOptions } from "ws";
import { Agent } from "undici-types";
import { ConnectionOptions } from "node:tls";
export type CertificateType = "pem" | "pfx";
export interface ICertificate {
    toAgentOptions(): Agent.Options;
    toSocketOptions(): ConnectionOptions;
    toWebSocketOptions(): ClientOptions;
}
export declare abstract class Certificate implements ICertificate {
    static readonly PEM: CertificateType;
    static readonly PFX: CertificateType;
    protected _certificate: string | Buffer;
    protected _ca: string | Buffer;
    protected _passphrase?: string;
    static createFromOptions(options: IAuthOptions): ICertificate;
    static createPem(certificate: string | Buffer, passphrase?: string, ca?: string | Buffer): PemCertificate;
    static createPfx(certificate: string | Buffer, passphrase?: string, ca?: string | Buffer): PfxCertificate;
    protected constructor(certificate: string | Buffer, passphrase?: string, ca?: string | Buffer);
    toAgentOptions(): Agent.Options;
    toSocketOptions(): ConnectionOptions;
    toWebSocketOptions(): ClientOptions;
}
export declare class PemCertificate extends Certificate {
    private readonly _certToken;
    private readonly _keyToken;
    protected _key: string;
    constructor(certificate: string | Buffer, passphrase?: string, ca?: string | Buffer);
    toAgentOptions(): Agent.Options;
    toSocketOptions(): ConnectionOptions;
    toWebSocketOptions(): ClientOptions;
    protected _fetchPart(token: string): string;
}
export declare class PfxCertificate extends Certificate {
    constructor(certificate: string | Buffer, passphrase?: string, ca?: string | Buffer);
    toAgentOptions(): Agent.Options;
    toSocketOptions(): ConnectionOptions;
    toWebSocketOptions(): ClientOptions;
}
//# sourceMappingURL=Certificate.d.ts.map