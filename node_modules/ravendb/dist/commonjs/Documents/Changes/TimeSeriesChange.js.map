{"version": 3, "file": "TimeSeriesChange.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/TimeSeriesChange.ts"], "names": [], "mappings": "", "sourcesContent": ["import { DatabaseChange } from \"./DatabaseChange.js\";\r\n\r\nexport type TimeSeriesChangeTypes = \"None\" | \"Put\" | \"Delete\" | \"Mixed\";\r\n\r\nexport interface TimeSeriesChange extends DatabaseChange {\r\n    name: string;\r\n    from: Date;\r\n    to: Date;\r\n    documentId: string;\r\n    changeVector: string;\r\n    type: TimeSeriesChangeTypes;\r\n    collectionName: string;\r\n}\r\n"]}