{"version": 3, "file": "IDatabaseChanges.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/IDatabaseChanges.ts"], "names": [], "mappings": "", "sourcesContent": ["import { DocumentChange } from \"./DocumentChange.js\";\r\nimport { OperationStatusChange } from \"./OperationStatusChange.js\";\r\nimport { IndexChange } from \"./IndexChange.js\";\r\nimport { IConnectableChanges } from \"./IConnectableChanges.js\";\r\nimport { CounterChange } from \"./CounterChange.js\";\r\nimport { TimeSeriesChange } from \"./TimeSeriesChange.js\";\r\nimport { IIndexChanges } from \"./IIndexChanges.js\";\r\nimport { IOperationChanges } from \"./IOperationChanges.js\";\r\nimport { ICounterChanges } from \"./ICounterChanges.js\";\r\nimport { ITimeSeriesChanges } from \"./ITimeSeriesChanges.js\";\r\nimport { IDocumentChanges } from \"./IDocumentChanges.js\";\r\n\r\nexport interface IDatabaseChanges extends IDocumentChanges<DocumentChange>,\r\n    IIndexChanges<IndexChange>,\r\n    IOperationChanges<OperationStatusChange>,\r\n    ICounterChanges<CounterChange>,\r\n    ITimeSeriesChanges<TimeSeriesChange>,\r\n    IConnectableChanges<IDatabaseChanges> {\r\n\r\n}\r\n"]}