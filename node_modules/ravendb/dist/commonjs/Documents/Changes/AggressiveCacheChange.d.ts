import { DatabaseChange } from "./DatabaseChange.js";
import { DocumentChange } from "./DocumentChange.js";
import { IndexChange } from "./IndexChange.js";
export declare class AggressiveCacheChange implements DatabaseChange {
    static readonly INSTANCE: AggressiveCacheChange;
    static shouldUpdateAggressiveCache(change: DocumentChange | IndexChange): boolean;
}
//# sourceMappingURL=AggressiveCacheChange.d.ts.map