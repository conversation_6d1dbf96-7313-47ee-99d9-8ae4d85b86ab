{"version": 3, "file": "IOperationChanges.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/IOperationChanges.ts"], "names": [], "mappings": "", "sourcesContent": ["import { IChangesObservable } from \"./IChangesObservable.js\";\r\n\r\nexport interface IOperationChanges<TChanges> {\r\n    /**\r\n     * Subscribe to changes for specified operation only.\r\n     */\r\n    forOperationId(operationId: number): IChangesObservable<TChanges>;\r\n\r\n    /**\r\n     * Subscribe to change for all operation statuses.\r\n     */\r\n    forAllOperations(): IChangesObservable<TChanges>;\r\n}"]}