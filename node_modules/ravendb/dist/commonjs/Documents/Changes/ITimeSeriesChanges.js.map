{"version": 3, "file": "ITimeSeriesChanges.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/ITimeSeriesChanges.ts"], "names": [], "mappings": "", "sourcesContent": ["import { IChangesObservable } from \"./IChangesObservable.js\";\r\n\r\nexport interface ITimeSeriesChanges<TChanges> {\r\n    /**\r\n     * Subscribe to changes for all timeseries.\r\n     */\r\n    forAllTimeSeries(): IChangesObservable<TChanges>;\r\n\r\n    /**\r\n     * Subscribe to changes for all timeseries with a given name.\r\n     * @param timeSeriesName Time series name\r\n     */\r\n    forTimeSeries(timeSeriesName: string): IChangesObservable<TChanges>;\r\n\r\n    /**\r\n     * Subscribe to changes for timeseries from a given document and with given name.\r\n     * @param documentId Document identifier\r\n     * @param timeSeriesName Time series name\r\n     */\r\n    forTimeSeriesOfDocument(documentId: string, timeSeriesName: string): IChangesObservable<TChanges>;\r\n\r\n    /**\r\n     * Subscribe to changes for timeseries from a given document.\r\n     * @param documentId Document identifier\r\n     */\r\n    forTimeSeriesOfDocument(documentId): IChangesObservable<TChanges>;\r\n}"]}