"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AggressiveCacheChange = void 0;
class AggressiveCacheChange {
    static INSTANCE = new AggressiveCacheChange();
    static shouldUpdateAggressiveCache(change) {
        return change.type === "Put" || change.type === "Delete" || change.type === "BatchCompleted" || change.type === "IndexRemoved";
    }
}
exports.AggressiveCacheChange = AggressiveCacheChange;
//# sourceMappingURL=AggressiveCacheChange.js.map