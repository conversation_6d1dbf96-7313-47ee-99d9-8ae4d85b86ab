import { ChangesType, IChangesConnectionState } from "./IChangesConnectionState.js";
import { IChangesObservable } from "./IChangesObservable.js";
export declare class ChangesObservable<T, TConnectionState extends IChangesConnectionState<any>> implements IChangesObservable<T> {
    private readonly _type;
    private readonly _connectionState;
    private readonly _filter;
    private readonly _subscribers;
    private readonly _errorSubscribers;
    private _sendHandler;
    private _errorHandler;
    constructor(type: ChangesType, connectionState: TConnectionState, filter: (val: T) => boolean);
    on(event: "data", handler: (value: T) => void): this;
    on(event: "error", handler: (error: Error) => void): this;
    removeListener(event: "data", handler: (value: T) => void): this;
    removeListener(event: "error", handler: (error: Error) => void): this;
    off(event: "data", handler: (value: T) => void): this;
    off(event: "error", handler: (error: Error) => void): this;
    send(msg: T): void;
    error(e: Error): void;
    ensureSubscribedNow(): Promise<void>;
}
//# sourceMappingURL=ChangesObservable.d.ts.map