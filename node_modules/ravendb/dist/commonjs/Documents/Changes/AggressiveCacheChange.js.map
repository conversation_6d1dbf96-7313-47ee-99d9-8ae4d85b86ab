{"version": 3, "file": "AggressiveCacheChange.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/AggressiveCacheChange.ts"], "names": [], "mappings": ";;;AAIA,MAAa,qBAAqB;IACvB,MAAM,CAAU,QAAQ,GAAG,IAAI,qBAAqB,EAAE,CAAC;IAEvD,MAAM,CAAC,2BAA2B,CAAC,MAAoC;QAC1E,OAAO,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,IAAI,MAAM,CAAC,IAAI,KAAK,cAAc,CAAC;IACnI,CAAC;;AALL,sDAMC", "sourcesContent": ["import { DatabaseChange } from \"./DatabaseChange.js\";\r\nimport { DocumentChange } from \"./DocumentChange.js\";\r\nimport { IndexChange } from \"./IndexChange.js\";\r\n\r\nexport class AggressiveCacheChange implements DatabaseChange {\r\n    public static readonly INSTANCE = new AggressiveCacheChange();\r\n\r\n    public static shouldUpdateAggressiveCache(change: DocumentChange | IndexChange): boolean {\r\n        return change.type === \"Put\" || change.type === \"Delete\" || change.type === \"BatchCompleted\" || change.type === \"IndexRemoved\";\r\n    }\r\n}\r\n"]}