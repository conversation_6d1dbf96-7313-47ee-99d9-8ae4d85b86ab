import { IDatabaseChanges } from "./IDatabaseChanges.js";
import { IChangesObservable } from "./IChangesObservable.js";
import { IndexChange } from "./IndexChange.js";
import { CounterChange } from "./CounterChange.js";
import { DocumentChange } from "./DocumentChange.js";
import { OperationStatusChange } from "./OperationStatusChange.js";
import { WebSocket } from "ws";
import { RequestExecutor } from "../../Http/RequestExecutor.js";
import { ObjectTypeDescriptor } from "../../Types/index.js";
import { TimeSeriesChange } from "./TimeSeriesChange.js";
export declare class DatabaseChanges implements IDatabaseChanges {
    private _emitter;
    private _commandId;
    private readonly _onConnectionStatusChangedWrapped;
    private _semaphore;
    private readonly _requestExecutor;
    private readonly _conventions;
    private readonly _database;
    private readonly _onDispose;
    private _client;
    private readonly _task;
    private _isCanceled;
    private _tcs;
    private readonly _confirmations;
    private readonly _counters;
    private _immediateConnection;
    private _serverNode;
    private _nodeIndex;
    private _url;
    constructor(requestExecutor: RequestExecutor, databaseName: string, onDispose: () => void, nodeTag: string);
    static createClientWebSocket(requestExecutor: RequestExecutor, url: string): Promise<WebSocket>;
    private _onConnectionStatusChanged;
    get connected(): boolean;
    on(eventName: "connectionStatus", handler: () => void): this;
    on(eventName: "error", handler: (error: Error) => void): this;
    off(eventName: "connectionStatus", handler: () => void): this;
    off(eventName: "error", handler: (error: Error) => void): this;
    ensureConnectedNow(): Promise<IDatabaseChanges>;
    forIndex(indexName: string): IChangesObservable<IndexChange>;
    get lastConnectionStateException(): Error;
    forDocument(docId: string): IChangesObservable<DocumentChange>;
    forAllDocuments(): IChangesObservable<DocumentChange>;
    forOperationId(operationId: number): IChangesObservable<OperationStatusChange>;
    forAllOperations(): IChangesObservable<OperationStatusChange>;
    forAllIndexes(): IChangesObservable<IndexChange>;
    forDocumentsStartingWith(docIdPrefix: string): IChangesObservable<DocumentChange>;
    forDocumentsInCollection(collectionName: string): IChangesObservable<DocumentChange>;
    forDocumentsInCollection<T extends object>(type: ObjectTypeDescriptor<T>): any;
    dispose(): void;
    private _getOrAddConnectionState;
    private _send;
    private _doWork;
    private _doWorkInternal;
    private _reconnectClient;
    private _processChanges;
    private _notifySubscribers;
    private _notifyAboutError;
    forAllCounters(): IChangesObservable<CounterChange>;
    forCounter(counterName: string): IChangesObservable<CounterChange>;
    forCounterOfDocument(documentId: string, counterName: string): IChangesObservable<CounterChange>;
    forCountersOfDocument(documentId: string): IChangesObservable<CounterChange>;
    forAllTimeSeries(): IChangesObservable<TimeSeriesChange>;
    forTimeSeries(timeSeriesName: string): IChangesObservable<TimeSeriesChange>;
    forTimeSeriesOfDocument(documentId: string): any;
    forTimeSeriesOfDocument(documentId: string, timeSeriesName: string): any;
    private _forTimeSeriesOfDocumentInternal;
    private _forTimeSeriesOfDocumentWithNameInternal;
}
//# sourceMappingURL=DatabaseChanges.d.ts.map