{"version": 3, "file": "DocumentChange.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/DocumentChange.ts"], "names": [], "mappings": "", "sourcesContent": ["import { DatabaseChange } from \"./DatabaseChange.js\";\r\n\r\nexport interface DocumentChange extends DatabaseChange {\r\n    type: DocumentChangeTypes;\r\n    id: string;\r\n    collectionName: string;\r\n    changeVector: string;\r\n}\r\n\r\nexport type DocumentChangeTypes =\r\n    \"None\"\r\n    | \"Put\"\r\n    | \"Delete\"\r\n    | \"Conflict\"\r\n    | \"Common\";\r\n"]}