{"version": 3, "file": "IChangesConnectionState.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/IChangesConnectionState.ts"], "names": [], "mappings": "", "sourcesContent": ["import { IDisposable } from \"../../Types/Contracts.js\";\r\n\r\nexport interface IChangesConnectionState<T> extends IDisposable {\r\n    inc(): void;\r\n\r\n    dec(): void;\r\n\r\n    error(e: Error): void;\r\n\r\n    ensureSubscribedNow(): Promise<void>;\r\n\r\n    addOnChangeNotification(type: ChangesType, handler: (value: T) => void);\r\n\r\n    removeOnChangeNotification(type: ChangesType, handler: (value: T) => void);\r\n\r\n    addOnError(handler: (value: Error) => void);\r\n\r\n    removeOnError(handler: (value: Error) => void);\r\n}\r\n\r\nexport type ChangesType = \"Document\" | \"Index\" | \"Operation\" | \"Counter\" | \"TimeSeries\" | \"AggressiveCache\";\r\n"]}