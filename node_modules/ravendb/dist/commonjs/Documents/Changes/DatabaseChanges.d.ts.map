{"version": 3, "file": "DatabaseChanges.d.ts", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/DatabaseChanges.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAC7D,OAAO,EAAE,WAAW,EAAkB,MAAM,kBAAkB,CAAC;AAC/D,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,qBAAqB,EAAE,MAAM,4BAA4B,CAAC;AAInE,OAAO,EAAE,SAAS,EAAuB,MAAM,IAAI,CAAC;AASpD,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAGhE,OAAO,EAAE,oBAAoB,EAAkB,MAAM,sBAAsB,CAAC;AAG5E,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAKzD,qBAAa,eAAgB,YAAW,gBAAgB;IAEpD,OAAO,CAAC,QAAQ,CAAsB;IACtC,OAAO,CAAC,UAAU,CAAa;IAC/B,OAAO,CAAC,QAAQ,CAAC,iCAAiC,CAAa;IAE/D,OAAO,CAAC,UAAU,CAAmB;IAErC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAkB;IACnD,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAsB;IACnD,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAS;IAEnC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAa;IACxC,OAAO,CAAC,OAAO,CAAY;IAE3B,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;IACvB,OAAO,CAAC,WAAW,CAAS;IAC5B,OAAO,CAAC,IAAI,CAA2B;IAEvC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAuE;IACtG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAmD;IAC7E,OAAO,CAAC,oBAAoB,CAAa;IAEzC,OAAO,CAAC,WAAW,CAAa;IAChC,OAAO,CAAC,UAAU,CAAS;IAC3B,OAAO,CAAC,IAAI,CAAS;gBAET,eAAe,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,IAAI,EAAE,OAAO,EAAE,MAAM;WAYtF,qBAAqB,CAAC,eAAe,EAAE,eAAe,EAAE,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC;YAc9F,0BAA0B;IAmBxC,IAAW,SAAS,YAEnB;IAEM,EAAE,CAAC,SAAS,EAAE,kBAAkB,EAAE,OAAO,EAAE,MAAM,IAAI,GAAG,IAAI;IAC5D,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,GAAG,IAAI;IAM7D,GAAG,CAAC,SAAS,EAAE,kBAAkB,EAAE,OAAO,EAAE,MAAM,IAAI,GAAG,IAAI;IAC7D,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,GAAG,IAAI;IAM9D,kBAAkB,IAAI,OAAO,CAAC,gBAAgB,CAAC;IAI/C,QAAQ,CAAC,SAAS,EAAE,MAAM,GAAG,kBAAkB,CAAC,WAAW,CAAC;IAanE,IAAW,4BAA4B,IAAI,KAAK,CAQ/C;IAEM,WAAW,CAAC,KAAK,EAAE,MAAM,GAAG,kBAAkB,CAAC,cAAc,CAAC;IAW9D,eAAe,IAAI,kBAAkB,CAAC,cAAc,CAAC;IAOrD,cAAc,CAAC,WAAW,EAAE,MAAM,GAAG,kBAAkB,CAAC,qBAAqB,CAAC;IAQ9E,gBAAgB,IAAI,kBAAkB,CAAC,qBAAqB,CAAC;IAQ7D,aAAa,IAAI,kBAAkB,CAAC,WAAW,CAAC;IAMhD,wBAAwB,CAAC,WAAW,EAAE,MAAM,GAAG,kBAAkB,CAAC,cAAc,CAAC;IAYjF,wBAAwB,CAAC,cAAc,EAAE,MAAM,GAAG,kBAAkB,CAAC,cAAc,CAAC;IACpF,wBAAwB,CAAC,CAAC,SAAS,MAAM,EAAE,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC;IAmBxE,OAAO,IAAI,IAAI;IAwBtB,OAAO,CAAC,wBAAwB;IAuChC,OAAO,CAAC,KAAK;YAyCC,OAAO;YAkBP,eAAe;IA6D7B,OAAO,CAAC,gBAAgB;YAWV,eAAe;IAiE7B,OAAO,CAAC,kBAAkB;IA+D1B,OAAO,CAAC,iBAAiB;IAYlB,cAAc,IAAI,kBAAkB,CAAC,aAAa,CAAC;IAOnD,UAAU,CAAC,WAAW,EAAE,MAAM,GAAG,kBAAkB,CAAC,aAAa,CAAC;IAYlE,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,kBAAkB,CAAC,aAAa,CAAC;IAsBhG,qBAAqB,CAAC,UAAU,EAAE,MAAM,GAAG,kBAAkB,CAAC,aAAa,CAAC;IAgB5E,gBAAgB,IAAI,kBAAkB,CAAC,gBAAgB,CAAC;IAaxD,aAAa,CAAC,cAAc,EAAE,MAAM,GAAG,kBAAkB,CAAC,gBAAgB,CAAC;IAgB3E,uBAAuB,CAAC,UAAU,EAAE,MAAM;IAC1C,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM;IASzE,OAAO,CAAC,gCAAgC;IAiBxC,OAAO,CAAC,wCAAwC;CAwBnD"}