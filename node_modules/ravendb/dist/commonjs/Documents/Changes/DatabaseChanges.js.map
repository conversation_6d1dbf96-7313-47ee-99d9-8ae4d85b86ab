{"version": 3, "file": "DatabaseChanges.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/DatabaseChanges.ts"], "names": [], "mappings": ";;;AAMA,6EAAuE;AACvE,iEAA2D;AAC3D,wDAAuD;AACvD,2BAAoD;AACpD,+DAAyD;AACzD,6CAA2C;AAC3C,iEAAqD;AAErD,qEAAkE;AAClE,8DAAwD;AACxD,+DAAyD;AAIzD,4DAAsD;AAEtD,wFAAkF;AAClF,2DAAqD;AAErD,yEAAmE;AACnE,6DAAuD;AACvD,2DAAqD;AAErD,MAAa,eAAe;IAEhB,QAAQ,GAAG,IAAI,0BAAY,EAAE,CAAC;IAC9B,UAAU,GAAW,CAAC,CAAC;IACd,iCAAiC,CAAa;IAEvD,UAAU,GAAG,IAAI,wBAAS,EAAE,CAAC;IAEpB,gBAAgB,CAAkB;IAClC,YAAY,CAAsB;IAClC,SAAS,CAAS;IAElB,UAAU,CAAa;IAChC,OAAO,CAAY;IAEV,KAAK,CAAC;IACf,WAAW,GAAG,KAAK,CAAC;IACpB,IAAI,CAA2B;IAEtB,cAAc,GAA6D,IAAI,GAAG,EAAE,CAAC;IACrF,SAAS,GAAyC,IAAI,GAAG,EAAE,CAAC,CAAC,0CAA0C;IAChH,oBAAoB,GAAW,CAAC,CAAC;IAEjC,WAAW,CAAa;IACxB,UAAU,CAAS;IACnB,IAAI,CAAS;IAErB,YAAY,eAAgC,EAAE,YAAoB,EAAE,SAAqB,EAAE,OAAe;QACtG,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,eAAe,CAAC,WAAW,CAAC;QAChD,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC;QAE9B,IAAI,CAAC,IAAI,GAAG,IAAA,sBAAK,GAAoB,CAAC;QACtC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,iCAAiC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACjF,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAC7E,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,eAAgC,EAAE,GAAW;QACnF,MAAM,WAAW,GAAG,eAAe,CAAC,cAAc,EAAE,CAAC;QACrD,IAAI,OAAO,GAAG,SAA0B,CAAC;QAEzC,IAAI,WAAW,EAAE,CAAC;YACd,MAAM,WAAW,GAAG,4BAAW,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAC/D,OAAO,GAAG,WAAW,CAAC,kBAAkB,EAAE,CAAC;QAC/C,CAAC;QAED,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC;QAEzC,OAAO,IAAI,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;IAEO,KAAK,CAAC,0BAA0B;QACpC,MAAM,kBAAkB,GAAG,IAAA,mCAAgB,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE7D,IAAI,CAAC;YACD,MAAM,kBAAkB,CAAC,OAAO,CAAC;YAEjC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACxB,OAAO;YACX,CAAC;YAED,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACxB,IAAI,CAAC,IAAI,GAAG,IAAA,sBAAK,GAAoB,CAAC;YAC1C,CAAC;QACL,CAAC;gBAAS,CAAC;YACP,kBAAkB,CAAC,OAAO,EAAE,CAAC;QACjC,CAAC;IACL,CAAC;IAED,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,cAAS,CAAC,IAAI,CAAC;IACtE,CAAC;IAIM,EAAE,CAAC,SAAuC,EAAE,OAAO;QACtD,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC;IAChB,CAAC;IAIM,GAAG,CAAC,SAAuC,EAAE,OAAO;QACvD,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,kBAAkB;QACrB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAEM,QAAQ,CAAC,SAAiB;QAC7B,IAAI,0BAAU,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3C,IAAA,qBAAU,EAAC,0BAA0B,EAAE,yCAAyC,CAAC,CAAC;QACtF,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,GAAG,SAAS,EAChE,aAAa,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC;QAE/C,OAAO,IAAI,wCAAiB,CAAuC,OAAO,EAAE,OAAO,EAC/E,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI;eAC1B,YAAY,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC;IACxF,CAAC;IAED,IAAW,4BAA4B;QACnC,KAAK,MAAM,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC;YACxD,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACpB,OAAO,OAAO,CAAC,SAAS,CAAC;YAC7B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,WAAW,CAAC,KAAa;QAC5B,IAAI,0BAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;YACvC,IAAA,qBAAU,EAAC,0BAA0B,EAAE,0CAA0C,CAAC,CAAC;QACvF,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,GAAG,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;QAElG,OAAO,IAAI,wCAAiB,CAA0C,UAAU,EAAE,OAAO,EACrF,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,EAAE,IAAI,YAAY,CAAC,EAAE,CAAC,iBAAiB,EAAE,KAAK,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC;IAC9G,CAAC;IAEM,eAAe;QAClB,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,YAAY,EAClE,cAAc,EAAE,IAAI,CAAC,CAAC;QAC1B,OAAO,IAAI,wCAAiB,CAA0C,UAAU,EAAE,OAAO,EACrF,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IAEM,cAAc,CAAC,WAAmB;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,aAAa,GAAG,WAAW,EACrE,iBAAiB,EAAE,mBAAmB,EAAE,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;QAEpE,OAAO,IAAI,wCAAiB,CAAiD,WAAW,EAAE,OAAO,EAC7F,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,WAAW,KAAK,WAAW,CAAC,CAAC;IAClE,CAAC;IAEM,gBAAgB;QACnB,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,EAAE,kBAAkB,EAC9E,oBAAoB,EAAE,IAAI,CAAC,CAAC;QAEhC,OAAO,IAAI,wCAAiB,CAAiD,WAAW,EAAE,OAAO,EAC7F,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IAEM,aAAa;QAChB,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,aAAa,EAAE,eAAe,EACxE,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAC7B,OAAO,IAAI,wCAAiB,CAAuC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;IACrG,CAAC;IAEM,wBAAwB,CAAC,WAAmB;QAC/C,IAAI,0BAAU,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE,CAAC;YAC7C,IAAA,qBAAU,EAAC,0BAA0B,EAAE,0CAA0C,CAAC,CAAC;QACvF,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,WAAW,GAAG,WAAW,EACnE,cAAc,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAAC;QACnD,OAAO,IAAI,wCAAiB,CAA0C,UAAU,EAAE,OAAO,EACrF,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,EAAE;eACxB,YAAY,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;IAChG,CAAC;IAIM,wBAAwB,CAC3B,0BAA4D;QAC5D,MAAM,cAAc,GAAW,OAAO,0BAA0B,KAAK,QAAQ;YACzE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,0BAA0B,CAAC;YACxE,CAAC,CAAC,0BAA0B,CAAC;QAEjC,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,IAAA,qBAAU,EAAC,0BAA0B,EAAE,+BAA+B,CAAC,CAAC;QAC5E,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,cAAc,GAAG,cAAc,EACzE,kBAAkB,EAAE,oBAAoB,EAAE,cAAc,CAAC,CAAC;QAE9D,OAAO,IAAI,wCAAiB,CAA0C,UAAU,EAAE,OAAO,EACrF,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,cAAc;eACpC,cAAc,CAAC,iBAAiB,EAAE,KAAK,YAAY,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,CAAC;IACvG,CAAC;IAEM,OAAO;QACV,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC;YACtD,YAAY,CAAC,MAAM,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC1C,KAAK,CAAC,OAAO,EAAE,CAAC;QACpB,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QAEvB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACvC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,kBAAkB,EAAE,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAEzF,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,EAAE,CAAC;QACtB,CAAC;IACL,CAAC;IAEO,wBAAwB,CAC5B,IAAY,EAAE,YAAoB,EAAE,cAAc,EAAE,KAAa,EAAE,SAAmB,IAAI;QAG1F,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,IAAI,OAAgC,CAAC;QAErC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,MAAM,eAAe,GAAG,IAAI,oDAAuB,CAC/C,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,KAAK,IAAI,EAAE;gBACtD,IAAI,CAAC;oBACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;wBACjB,MAAM,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;oBACpD,CAAC;gBACL,CAAC;gBAAC,MAAM,CAAC;oBACL,uDAAuD;oBACvD,mDAAmD;gBACvD,CAAC;gBAED,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACvC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC5B,KAAK,CAAC,OAAO,EAAE,CAAC;YACpB,CAAC,CAAC,CAAC;YACP,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;YAC1C,OAAO,GAAG,eAAe,CAAC;YAE1B,QAAQ,GAAG,IAAI,CAAC;QACpB,CAAC;aAAM,CAAC;YACJ,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,QAAQ,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QACrC,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,OAAe,EAAE,KAAa,EAAE,MAAgB;QAC1D,qDAAqD;QACrD,OAAO,IAAI,OAAO,CAAO,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;YAChD,IAAI,gBAAwB,CAAC;YAE7B,MAAM,kBAAkB,GAAG,IAAA,mCAAgB,EAAC,IAAI,CAAC,UAAU,EAAE;gBACzD,OAAO,EAAE,KAAK;gBACd,WAAW,EAAE,yBAAyB;aACzC,CAAC,CAAC;YAEH,IAAI,CAAC;gBACD,MAAM,kBAAkB,CAAC,OAAO,CAAC;gBAEjC,gBAAgB,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC;gBAErC,MAAM,OAAO,GAAG;oBACZ,SAAS,EAAE,gBAAgB;oBAC3B,OAAO,EAAE,OAAO;oBAChB,KAAK,EAAE,KAAK;iBACf,CAAC;gBAEF,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBAC1B,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;gBAC/B,CAAC;gBAED,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;gBAC/D,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;gBAEzD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACvC,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACX,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;oBACpB,MAAM,GAAG,CAAC;gBACd,CAAC;YACL,CAAC;oBAAS,CAAC;gBACP,IAAI,kBAAkB,EAAE,CAAC;oBACrB,kBAAkB,CAAC,OAAO,EAAE,CAAC;gBACjC,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC,CAAC;IACR,CAAC;IAEO,KAAK,CAAC,OAAO,CAAC,OAAe;QACjC,IAAI,aAAkC,CAAC;QACvC,IAAI,CAAC;YACD,aAAa,GAAG,OAAO,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,sBAAsB;gBAC/E,CAAC,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBACvD,CAAC,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;YACrD,IAAI,CAAC,UAAU,GAAG,aAAa,CAAC,YAAY,CAAC;YAC7C,IAAI,CAAC,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;QACjD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACpB,OAAO;QACX,CAAC;QAED,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;IACjC,CAAC;IAEO,KAAK,CAAC,eAAe;QACzB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,IAAI,YAAY,GAAG,KAAK,CAAC;QAEzB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,aAAa,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;YACrF,MAAM,GAAG,GAAG,0BAAU,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAElD,IAAI,CAAC,OAAO,GAAG,MAAM,eAAe,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;YAEvF,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;gBAC/B,YAAY,GAAG,IAAI,CAAC;gBACpB,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;gBAE9B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;oBAC5C,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;gBACrC,CAAC;gBAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE;gBACjC,IAAI,YAAY,EAAE,CAAC;oBACf,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAC3C,CAAC;gBACD,YAAY,GAAG,KAAK,CAAC;gBAErB,IAAI,CAAC;oBACD,IAAI,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;gBAC9H,CAAC;gBAAC,OAAO,EAAE,EAAE,CAAC;oBACV,IAAI,EAAE,CAAC,IAAI,KAAK,+BAA+B,EAAE,CAAC;wBAC9C,CAAC,GAAG,EAAE,CAAC;wBACP,MAAM,EAAE,CAAC;oBACb,CAAC;yBAAM,CAAC;wBACJ,yFAAyF;oBAC7F,CAAC;gBACL,CAAC;gBACD,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBAC1B,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;oBAC1B,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC,CAAC;gBACnD,CAAC;gBAED,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC;oBACjD,OAAO,CAAC,MAAM,EAAE,CAAC;gBACrB,CAAC;gBAED,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAChC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,IAAU,EAAE,EAAE;gBAC5C,MAAM,IAAI,CAAC,eAAe,CAAC,IAAc,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,gBAAgB;QACpB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAE9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,IAAY;QACtC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QACD,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAU,CAAC;QAEhD,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;YAChF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC7B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;gBAC1B,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;oBACzB,MAAM,KAAK,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,uBAAuB,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;oBACzF,KAAK,CAAC,UAAU,CAAC,sBAAQ,CAAC,IAAI,CAAC,CAAC;oBAEhC,MAAM,gBAAgB,GAAG,IAAI,sDAAwB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACxE,gBAAgB,CAAC,WAAW,GAAG,CAAC,CAAC;oBACjC,gBAAgB,CAAC,WAAW,GAAG,IAAI,CAAC;oBACpC,gBAAgB,CAAC,QAAQ,GAAG,uBAAuB,CAAC;oBACpD,+BAA+B;oBAC/B,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;oBACvD,SAAS;gBACb,CAAC;gBACD,IAAI,CAAC,IAAI,EAAE,CAAC;oBACR,SAAS;gBACb,CAAC;gBAED,QAAQ,IAAI,EAAE,CAAC;oBACX,KAAK,OAAO,CAAC,CAAC,CAAC;wBACX,MAAM,iBAAiB,GAAG,OAAO,CAAC,SAAS,CAAC;wBAC5C,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;wBAC1C,MAAM;oBACV,CAAC;oBACD,KAAK,SAAS,CAAC,CAAC,CAAC;wBACb,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;wBACpC,MAAM,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;wBAChE,IAAI,oBAAoB,EAAE,CAAC;4BACvB,oBAAoB,CAAC,OAAO,EAAE,CAAC;4BAC/B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;wBAC1C,CAAC;wBACD,MAAM;oBACV,CAAC;oBACD,OAAO,CAAC,CAAC,CAAC;wBACN,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;wBAC5B,IAAI,gBAAgB,GAAG,0BAAU,CAAC,mBAAmB,CAAC,KAAK,EAAE,EAAE,gBAAgB,EAAE,0BAAU,CAAC,KAAK,EAAE,CAAC,CAAC;wBACrG,IAAI,IAAI,KAAK,kBAAkB,EAAE,CAAC;4BAC9B,MAAM,eAAe,GAAG,gBAAoD,CAAC;4BAE7E,MAAM,SAAS,GAA8B;gCACzC,IAAI,EAAE,sBAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC;gCAC9C,EAAE,EAAE,sBAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC;6BAC7C,CAAC;4BAEF,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;wBAClE,CAAC;wBACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;wBAChD,MAAM;oBACV,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAA,qBAAU,EAAC,2BAA2B,EAAE,oDAAoD,EAAE,GAAG,CAAC,CAAC;QACvG,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,IAAY,EAAE,KAAU;QAC/C,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,uBAAuB,CAAC,CAAC,CAAC;gBAC3B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;oBAC1C,KAAK,CAAC,IAAI,CAAC,iBAAiB,EAAE,gDAAqB,CAAC,QAAQ,CAAC,CAAC;gBAClE,CAAC;gBACD,MAAM;YACV,CAAC;YACD,KAAK,gBAAgB,CAAC,CAAC,CAAC;gBACpB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;oBAC1C,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;gBAClC,CAAC;gBACD,MAAM;YACV,CAAC;YACD,KAAK,eAAe,CAAC,CAAC,CAAC;gBACnB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;oBAC1C,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;gBACjC,CAAC;gBACD,MAAM;YACV,CAAC;YACD,KAAK,kBAAkB,CAAC,CAAC,CAAC;gBACtB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;oBAC1C,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;gBACpC,CAAC;gBACD,MAAM;YACV,CAAC;YACD,KAAK,aAAa,CAAC,CAAC,CAAC;gBACjB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;oBAC1C,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC/B,CAAC;gBACD,MAAM;YACV,CAAC;YACD,KAAK,uBAAuB,CAAC,CAAC,CAAC;gBAC3B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;oBAC1C,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;gBACnC,CAAC;gBACD,MAAM;YACV,CAAC;YACD,KAAK,gBAAgB,CAAC,CAAC,CAAC;gBACpB,MAAM,cAAc,GAAG,KAAuB,CAAC;gBAC/C,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;gBAC9C,IAAI,eAAe,EAAE,CAAC;oBAClB,MAAM,IAAI,GAAG,IAAI,0BAAU,CAAC;wBACxB,GAAG,EAAE,cAAc,CAAC,GAAG;wBACvB,QAAQ,EAAE,cAAc,CAAC,QAAQ;qBACpC,CAAC,CAAC;oBAEH,MAAM,gBAAgB,GAAG,IAAI,sDAAwB,CAAC,IAAI,CAAC,CAAC;oBAC5D,gBAAgB,CAAC,WAAW,GAAG,CAAC,CAAC;oBACjC,gBAAgB,CAAC,WAAW,GAAG,IAAI,CAAC;oBACpC,gBAAgB,CAAC,QAAQ,GAAG,8BAA8B,CAAC;oBAE3D,wCAAwC;oBACxC,eAAe,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;gBACrD,CAAC;gBACD,MAAM;YACV,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACN,IAAA,qBAAU,EAAC,uBAAuB,CAAC,CAAC;YACxC,CAAC;QACL,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,CAAQ;QAC9B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAE/B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC1C,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;IACL,CAAC;IAEM,cAAc;QACjB,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,cAAc,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAC;QAC1G,MAAM,gBAAgB,GAAG,IAAI,wCAAiB,CAC1C,SAAS,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAC9C,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAEM,UAAU,CAAC,WAAmB;QACjC,IAAI,0BAAU,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE,CAAC;YAC7C,IAAA,qBAAU,EAAC,0BAA0B,EAAE,2CAA2C,CAAC,CAAC;QACxF,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CACzC,UAAU,GAAG,WAAW,EAAE,eAAe,EAAE,iBAAiB,EAAE,WAAW,CAAC,CAAC;QAC/E,MAAM,gBAAgB,GAAG,IAAI,wCAAiB,CACtC,SAAS,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE,CAAC,0BAAU,CAAC,gBAAgB,CAAC,WAAW,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;QACzG,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAEM,oBAAoB,CAAC,UAAkB,EAAE,WAAmB;QAC/D,IAAI,0BAAU,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5C,IAAA,qBAAU,EAAC,0BAA0B,EAAE,0CAA0C,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,0BAAU,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE,CAAC;YAC7C,IAAA,qBAAU,EAAC,0BAA0B,EAAE,2CAA2C,CAAC,CAAC;QACxF,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CACzC,WAAW,GAAG,UAAU,GAAG,WAAW,GAAG,WAAW,EACpD,wBAAwB,EACxB,0BAA0B,EAC1B,IAAI,EACJ,CAAE,UAAU,EAAE,WAAW,CAAE,CAAC,CAAC;QACjC,MAAM,gBAAgB,GAAG,IAAI,wCAAiB,CAC1C,SAAS,EAAE,OAAO,EAClB,YAAY,CAAC,EAAE,CAAC,0BAAU,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,CAAC,UAAU,CAAC;eACzE,0BAAU,CAAC,gBAAgB,CAAC,WAAW,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;QACxE,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAEM,qBAAqB,CAAC,UAAkB;QAC3C,IAAI,0BAAU,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5C,IAAA,qBAAU,EACN,0BAA0B,EAC1B,0CAA0C,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CACzC,WAAW,GAAG,UAAU,GAAG,UAAU,EAAE,yBAAyB,EAAE,2BAA2B,EAAE,UAAU,CAAC,CAAC;QAC/G,MAAM,gBAAgB,GAAG,IAAI,wCAAiB,CAC1C,SAAS,EACT,OAAO,EACP,YAAY,CAAC,EAAE,CAAC,0BAAU,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;QACtF,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAEM,gBAAgB;QACnB,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CACzC,gBAAgB,EAAE,sBAAsB,EAAE,wBAAwB,EAAE,IAAI,CAAC,CAAC;QAE9E,MAAM,gBAAgB,GAAG,IAAI,wCAAiB,CAC1C,YAAY,EACZ,OAAO,EACP,GAAG,EAAE,CAAC,IAAI,CACb,CAAC;QAEF,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAEM,aAAa,CAAC,cAAsB;QACvC,IAAI,0BAAU,CAAC,kBAAkB,CAAC,cAAc,CAAC,EAAE,CAAC;YAChD,IAAA,qBAAU,EAAC,0BAA0B,EAAE,8CAA8C,CAAC,CAAC;QAC3F,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CACzC,aAAa,GAAG,cAAc,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,cAAc,CAAC,CAAC;QAE9F,MAAM,gBAAgB,GAAG,IAAI,wCAAiB,CAC1C,YAAY,EACZ,OAAO,EACP,YAAY,CAAC,EAAE,CAAC,0BAAU,CAAC,gBAAgB,CAAC,cAAc,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;QAEpF,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAIM,uBAAuB,CAAC,UAAkB,EAAE,cAAuB;QACtE,IAAI,cAAc,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,wCAAwC,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QACrF,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,CAAC,gCAAgC,CAAC,UAAU,CAAC,CAAC;QAC7D,CAAC;IACL,CAAC;IAEO,gCAAgC,CAAC,UAAkB;QACvD,IAAI,0BAAU,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5C,IAAA,qBAAU,EAAC,0BAA0B,EAAE,0CAA0C,CAAC,CAAC;QACvF,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CACzC,WAAW,GAAG,UAAU,GAAG,aAAa,EAAE,+BAA+B,EAAE,iCAAiC,EAAE,UAAU,CAAC,CAAC;QAE9H,MAAM,gBAAgB,GAAG,IAAI,wCAAiB,CAC1C,YAAY,EACZ,OAAO,EACP,YAAY,CAAC,EAAE,CAAC,0BAAU,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,CAAC,UAAU,CAAC,CACnF,CAAC;QAEF,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAEO,wCAAwC,CAAC,UAAkB,EAAE,cAAsB;QACvF,IAAI,0BAAU,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5C,IAAA,qBAAU,EAAC,0BAA0B,EAAE,0CAA0C,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,0BAAU,CAAC,kBAAkB,CAAC,cAAc,CAAC,EAAE,CAAC;YAChD,IAAA,qBAAU,EAAC,0BAA0B,EAAE,8CAA8C,CAAC,CAAC;QAC3F,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CACzC,WAAW,GAAG,UAAU,GAAG,cAAc,GAAG,cAAc,EAC1D,2BAA2B,EAC3B,6BAA6B,EAAE,IAAI,EAAE,CAAE,UAAU,EAAE,cAAc,CAAE,CAAC,CAAC;QAEzE,MAAM,gBAAgB,GAAG,IAAI,wCAAiB,CAC1C,YAAY,EACZ,OAAO,EACP,YAAY,CAAC,EAAE,CAAC,0BAAU,CAAC,gBAAgB,CAAC,cAAc,EAAE,YAAY,CAAC,IAAI,CAAC;eACvE,0BAAU,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,CAAC,UAAU,CAAC,CAC1E,CAAC;QAEF,OAAO,gBAAgB,CAAC;IAC5B,CAAC;CAEJ;AAppBD,0CAopBC", "sourcesContent": ["import { IDatabaseChanges } from \"./IDatabaseChanges.js\";\r\nimport { IChangesObservable } from \"./IChangesObservable.js\";\r\nimport { IndexChange, TopologyChange } from \"./IndexChange.js\";\r\nimport { CounterChange } from \"./CounterChange.js\";\r\nimport { DocumentChange } from \"./DocumentChange.js\";\r\nimport { OperationStatusChange } from \"./OperationStatusChange.js\";\r\nimport { DatabaseConnectionState } from \"./DatabaseConnectionState.js\";\r\nimport { ChangesObservable } from \"./ChangesObservable.js\";\r\nimport { throwError } from \"../../Exceptions/index.js\";\r\nimport { WebSocket, ClientOptions, Data } from \"ws\";\r\nimport { StringUtil } from \"../../Utility/StringUtil.js\";\r\nimport { EventEmitter } from \"node:events\";\r\nimport { defer } from \"../../Utility/PromiseUtil.js\";\r\nimport { IDefer } from \"../../Utility/PromiseUtil.js\";\r\nimport { acquireSemaphore } from \"../../Utility/SemaphoreUtil.js\";\r\nimport { Certificate } from \"../../Auth/Certificate.js\";\r\nimport { ObjectUtil } from \"../../Utility/ObjectUtil.js\";\r\nimport CurrentIndexAndNode from \"../../Http/CurrentIndexAndNode.js\";\r\nimport { RequestExecutor } from \"../../Http/RequestExecutor.js\";\r\nimport { DocumentConventions } from \"../Conventions/DocumentConventions.js\";\r\nimport { ServerNode } from \"../../Http/ServerNode.js\";\r\nimport { ObjectTypeDescriptor, ServerResponse } from \"../../Types/index.js\";\r\nimport { UpdateTopologyParameters } from \"../../Http/UpdateTopologyParameters.js\";\r\nimport { TypeUtil } from \"../../Utility/TypeUtil.js\";\r\nimport { TimeSeriesChange } from \"./TimeSeriesChange.js\";\r\nimport { AggressiveCacheChange } from \"./AggressiveCacheChange.js\";\r\nimport { Semaphore } from \"../../Utility/Semaphore.js\";\r\nimport { DateUtil } from \"../../Utility/DateUtil.js\";\r\n\r\nexport class DatabaseChanges implements IDatabaseChanges {\r\n\r\n    private _emitter = new EventEmitter();\r\n    private _commandId: number = 0;\r\n    private readonly _onConnectionStatusChangedWrapped: () => void;\r\n\r\n    private _semaphore = new Semaphore();\r\n\r\n    private readonly _requestExecutor: RequestExecutor;\r\n    private readonly _conventions: DocumentConventions;\r\n    private readonly _database: string;\r\n\r\n    private readonly _onDispose: () => void;\r\n    private _client: WebSocket;\r\n\r\n    private readonly _task;\r\n    private _isCanceled = false;\r\n    private _tcs: IDefer<IDatabaseChanges>;\r\n\r\n    private readonly _confirmations: Map<number, { resolve: () => void, reject: () => void }> = new Map();\r\n    private readonly _counters: Map<string, DatabaseConnectionState> = new Map(); //TODO: use DatabaseChangesOptions as key?\r\n    private _immediateConnection: number = 0;\r\n\r\n    private _serverNode: ServerNode;\r\n    private _nodeIndex: number;\r\n    private _url: string;\r\n\r\n    constructor(requestExecutor: RequestExecutor, databaseName: string, onDispose: () => void, nodeTag: string) {\r\n        this._requestExecutor = requestExecutor;\r\n        this._conventions = requestExecutor.conventions;\r\n        this._database = databaseName;\r\n\r\n        this._tcs = defer<IDatabaseChanges>();\r\n        this._onDispose = onDispose;\r\n        this._onConnectionStatusChangedWrapped = () => this._onConnectionStatusChanged();\r\n        this._emitter.on(\"connectionStatus\", this._onConnectionStatusChangedWrapped);\r\n        this._task = this._doWork(nodeTag);\r\n    }\r\n\r\n    public static async createClientWebSocket(requestExecutor: RequestExecutor, url: string): Promise<WebSocket> {\r\n        const authOptions = requestExecutor.getAuthOptions();\r\n        let options = undefined as ClientOptions;\r\n\r\n        if (authOptions) {\r\n            const certificate = Certificate.createFromOptions(authOptions);\r\n            options = certificate.toWebSocketOptions();\r\n        }\r\n\r\n        const { WebSocket } = await import(\"ws\");\r\n\r\n        return new WebSocket(url, options);\r\n    }\r\n\r\n    private async _onConnectionStatusChanged() {\r\n        const acquiredSemContext = acquireSemaphore(this._semaphore);\r\n\r\n        try {\r\n            await acquiredSemContext.promise;\r\n\r\n            if (this.connected) {\r\n                this._tcs.resolve(this);\r\n                return;\r\n            }\r\n\r\n            if (this._tcs.isFulfilled) {\r\n                this._tcs = defer<IDatabaseChanges>();\r\n            }\r\n        } finally {\r\n            acquiredSemContext.dispose();\r\n        }\r\n    }\r\n\r\n    public get connected() {\r\n        return this._client && this._client.readyState === WebSocket.OPEN;\r\n    }\r\n\r\n    public on(eventName: \"connectionStatus\", handler: () => void): this;\r\n    public on(eventName: \"error\", handler: (error: Error) => void): this;\r\n    public on(eventName: \"connectionStatus\" | \"error\", handler): this {\r\n        this._emitter.addListener(eventName, handler);\r\n        return this;\r\n    }\r\n\r\n    public off(eventName: \"connectionStatus\", handler: () => void): this;\r\n    public off(eventName: \"error\", handler: (error: Error) => void): this;\r\n    public off(eventName: \"connectionStatus\" | \"error\", handler): this {\r\n        this._emitter.removeListener(eventName, handler);\r\n        return this;\r\n    }\r\n\r\n    public ensureConnectedNow(): Promise<IDatabaseChanges> {\r\n        return Promise.resolve(this._tcs.promise);\r\n    }\r\n\r\n    public forIndex(indexName: string): IChangesObservable<IndexChange> {\r\n        if (StringUtil.isNullOrWhitespace(indexName)) {\r\n            throwError(\"InvalidArgumentException\", \"IndexName cannot be null or whitespace.\");\r\n        }\r\n\r\n        const counter = this._getOrAddConnectionState(\"indexes/\" + indexName,\r\n            \"watch-index\", \"unwatch-index\", indexName);\r\n\r\n        return new ChangesObservable<IndexChange, DatabaseConnectionState>(\"Index\", counter,\r\n            notification => notification.name\r\n                && notification.name.toLocaleLowerCase() === indexName.toLocaleLowerCase());\r\n    }\r\n\r\n    public get lastConnectionStateException(): Error {\r\n        for (const counter of Array.from(this._counters.values())) {\r\n            if (counter.lastError) {\r\n                return counter.lastError;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    public forDocument(docId: string): IChangesObservable<DocumentChange> {\r\n        if (StringUtil.isNullOrWhitespace(docId)) {\r\n            throwError(\"InvalidArgumentException\", \"DocumentId cannot be null or whitespace.\");\r\n        }\r\n\r\n        const counter = this._getOrAddConnectionState(\"docs/\" + docId, \"watch-doc\", \"unwatch-doc\", docId);\r\n\r\n        return new ChangesObservable<DocumentChange, DatabaseConnectionState>(\"Document\", counter,\r\n            notification => notification.id && notification.id.toLocaleLowerCase() === docId.toLocaleLowerCase());\r\n    }\r\n\r\n    public forAllDocuments(): IChangesObservable<DocumentChange> {\r\n        const counter = this._getOrAddConnectionState(\"all-docs\", \"watch-docs\",\r\n            \"unwatch-docs\", null);\r\n        return new ChangesObservable<DocumentChange, DatabaseConnectionState>(\"Document\", counter,\r\n            () => true);\r\n    }\r\n\r\n    public forOperationId(operationId: number): IChangesObservable<OperationStatusChange> {\r\n        const counter = this._getOrAddConnectionState(\"operations/\" + operationId,\r\n            \"watch-operation\", \"unwatch-operation\", operationId.toString());\r\n\r\n        return new ChangesObservable<OperationStatusChange, DatabaseConnectionState>(\"Operation\", counter,\r\n            notification => notification.operationId === operationId);\r\n    }\r\n\r\n    public forAllOperations(): IChangesObservable<OperationStatusChange> {\r\n        const counter = this._getOrAddConnectionState(\"all-operations\", \"watch-operations\",\r\n            \"unwatch-operations\", null);\r\n\r\n        return new ChangesObservable<OperationStatusChange, DatabaseConnectionState>(\"Operation\", counter,\r\n            () => true);\r\n    }\r\n\r\n    public forAllIndexes(): IChangesObservable<IndexChange> {\r\n        const counter = this._getOrAddConnectionState(\"all-indexes\", \"watch-indexes\",\r\n            \"unwatch-indexes\", null);\r\n        return new ChangesObservable<IndexChange, DatabaseConnectionState>(\"Index\", counter, () => true);\r\n    }\r\n\r\n    public forDocumentsStartingWith(docIdPrefix: string): IChangesObservable<DocumentChange> {\r\n        if (StringUtil.isNullOrWhitespace(docIdPrefix)) {\r\n            throwError(\"InvalidArgumentException\", \"DocumentId cannot be null or whitespace.\");\r\n        }\r\n\r\n        const counter = this._getOrAddConnectionState(\"prefixes/\" + docIdPrefix,\r\n            \"watch-prefix\", \"unwatch-prefix\", docIdPrefix);\r\n        return new ChangesObservable<DocumentChange, DatabaseConnectionState>(\"Document\", counter,\r\n            notification => notification.id\r\n                && notification.id.toLocaleLowerCase().startsWith(docIdPrefix.toLocaleLowerCase()));\r\n    }\r\n\r\n    public forDocumentsInCollection(collectionName: string): IChangesObservable<DocumentChange>;\r\n    public forDocumentsInCollection<T extends object>(type: ObjectTypeDescriptor<T>);\r\n    public forDocumentsInCollection<T extends object = object>(\r\n        collectionNameOrDescriptor: string | ObjectTypeDescriptor<T>): IChangesObservable<DocumentChange> {\r\n        const collectionName: string = typeof collectionNameOrDescriptor !== \"string\"\r\n            ? this._conventions.getCollectionNameForType(collectionNameOrDescriptor)\r\n            : collectionNameOrDescriptor;\r\n\r\n        if (!collectionName) {\r\n            throwError(\"InvalidArgumentException\", \"CollectionName cannot be null\");\r\n        }\r\n\r\n        const counter = this._getOrAddConnectionState(\"collections/\" + collectionName,\r\n            \"watch-collection\", \"unwatch-collection\", collectionName);\r\n\r\n        return new ChangesObservable<DocumentChange, DatabaseConnectionState>(\"Document\", counter,\r\n            notification => notification.collectionName\r\n                && collectionName.toLocaleLowerCase() === notification.collectionName.toLocaleLowerCase());\r\n    }\r\n\r\n    public dispose(): void {\r\n        for (const confirmation of this._confirmations.values()) {\r\n            confirmation.reject();\r\n        }\r\n\r\n        this._isCanceled = true;\r\n        if (this._client) {\r\n            this._client.close();\r\n        }\r\n\r\n        for (const value of this._counters.values()) {\r\n            value.dispose();\r\n        }\r\n\r\n        this._counters.clear();\r\n\r\n        this._emitter.emit(\"connectionStatus\");\r\n        this._emitter.removeListener(\"connectionStatus\", this._onConnectionStatusChangedWrapped);\r\n\r\n        if (this._onDispose) {\r\n            this._onDispose();\r\n        }\r\n    }\r\n\r\n    private _getOrAddConnectionState(\r\n        name: string, watchCommand: string, unwatchCommand, value: string, values: string[] = null):\r\n        DatabaseConnectionState {\r\n\r\n        let newValue = false;\r\n\r\n        let counter: DatabaseConnectionState;\r\n\r\n        if (!this._counters.has(name)) {\r\n            const connectionState = new DatabaseConnectionState(\r\n                () => this._send(watchCommand, value, values), async () => {\r\n                    try {\r\n                        if (this.connected) {\r\n                            await this._send(unwatchCommand, value, values);\r\n                        }\r\n                    } catch {\r\n                        // if we are not connected then we unsubscribed already\r\n                        // because connections drops with all subscriptions\r\n                    }\r\n\r\n                    const state = this._counters.get(name);\r\n                    this._counters.delete(name);\r\n                    state.dispose();\r\n                });\r\n            this._counters.set(name, connectionState);\r\n            counter = connectionState;\r\n\r\n            newValue = true;\r\n        } else {\r\n            counter = this._counters.get(name);\r\n        }\r\n\r\n        if (newValue && this._immediateConnection) {\r\n            counter.set(counter.onConnect());\r\n        }\r\n\r\n        return counter;\r\n    }\r\n\r\n    private _send(command: string, value: string, values: string[]): Promise<void> {\r\n        // eslint-disable-next-line no-async-promise-executor\r\n        return new Promise<void>((async (resolve, reject) => {\r\n            let currentCommandId: number;\r\n\r\n            const acquiredSemContext = acquireSemaphore(this._semaphore, {\r\n                timeout: 15000,\r\n                contextName: \"DatabaseChanges._send()\"\r\n            });\r\n\r\n            try {\r\n                await acquiredSemContext.promise;\r\n\r\n                currentCommandId = ++this._commandId;\r\n\r\n                const payload = {\r\n                    CommandId: currentCommandId,\r\n                    Command: command,\r\n                    Param: value\r\n                };\r\n\r\n                if (values && values.length) {\r\n                    payload[\"Params\"] = values;\r\n                }\r\n\r\n                this._confirmations.set(currentCommandId, { resolve, reject });\r\n                const payloadAsString = JSON.stringify(payload, null, 0);\r\n\r\n                this._client.send(payloadAsString);\r\n            } catch (err) {\r\n                if (!this._isCanceled) {\r\n                    throw err;\r\n                }\r\n            } finally {\r\n                if (acquiredSemContext) {\r\n                    acquiredSemContext.dispose();\r\n                }\r\n            }\r\n        }));\r\n    }\r\n\r\n    private async _doWork(nodeTag: string): Promise<void> {\r\n        let preferredNode: CurrentIndexAndNode;\r\n        try {\r\n            preferredNode = nodeTag || this._requestExecutor.conventions.disableTopologyUpdates\r\n                ? await this._requestExecutor.getRequestedNode(nodeTag)\r\n                : await this._requestExecutor.getPreferredNode();\r\n            this._nodeIndex = preferredNode.currentIndex;\r\n            this._serverNode = preferredNode.currentNode;\r\n        } catch (e) {\r\n            this._emitter.emit(\"connectionStatus\");\r\n            this._notifyAboutError(e);\r\n            this._tcs.reject(e);\r\n            return;\r\n        }\r\n\r\n        await this._doWorkInternal();\r\n    }\r\n\r\n    private async _doWorkInternal(): Promise<void> {\r\n        if (this._isCanceled) {\r\n            return;\r\n        }\r\n\r\n        let wasConnected = false;\r\n\r\n        if (!this.connected) {\r\n            const urlString = this._serverNode.url + \"/databases/\" + this._database + \"/changes\";\r\n            const url = StringUtil.toWebSocketPath(urlString);\r\n\r\n            this._client = await DatabaseChanges.createClientWebSocket(this._requestExecutor, url);\r\n\r\n            this._client.on(\"open\", async () => {\r\n                wasConnected = true;\r\n                this._immediateConnection = 1;\r\n\r\n                for (const counter of this._counters.values()) {\r\n                    counter.set(counter.onConnect());\r\n                }\r\n\r\n                this._emitter.emit(\"connectionStatus\");\r\n            });\r\n\r\n            this._client.on(\"error\", async (e) => {\r\n                if (wasConnected) {\r\n                    this._emitter.emit(\"connectionStatus\");\r\n                }\r\n                wasConnected = false;\r\n\r\n                try {\r\n                    this._serverNode = await this._requestExecutor.handleServerNotResponsive(this._url, this._serverNode, this._nodeIndex, e);\r\n                } catch (ee) {\r\n                    if (ee.name === \"DatabaseDoesNotExistException\") {\r\n                        e = ee;\r\n                        throw ee;\r\n                    } else {\r\n                        //We don't want to stop observe for changes if server down. we will wait for one to be up\r\n                    }\r\n                }\r\n                this._notifyAboutError(e);\r\n            });\r\n\r\n            this._client.on(\"close\", () => {\r\n                if (this._reconnectClient()) {\r\n                    setTimeout(() => this._doWorkInternal(), 1000);\r\n                }\r\n\r\n                for (const confirm of this._confirmations.values()) {\r\n                    confirm.reject();\r\n                }\r\n\r\n                this._confirmations.clear();\r\n            });\r\n\r\n            this._client.on(\"message\", async (data: Data) => {\r\n                await this._processChanges(data as string);\r\n            });\r\n        }\r\n    }\r\n\r\n    private _reconnectClient(): boolean {\r\n        if (this._isCanceled) {\r\n            return false;\r\n        }\r\n\r\n        this._client.close();\r\n        this._immediateConnection = 0;\r\n\r\n        return true;\r\n    }\r\n\r\n    private async _processChanges(data: string): Promise<void> {\r\n        if (this._isCanceled) {\r\n            return;\r\n        }\r\n        const payloadParsed = JSON.parse(data) as any[];\r\n\r\n        try {\r\n            const messages = Array.isArray(payloadParsed) ? payloadParsed : [payloadParsed];\r\n            for (const message of messages) {\r\n                const type = message.Type;\r\n                if (message.TopologyChange) {\r\n                    const state = this._getOrAddConnectionState(\"Topology\", \"watch-topology-change\", \"\", \"\");\r\n                    state.addOnError(TypeUtil.NOOP);\r\n\r\n                    const updateParameters = new UpdateTopologyParameters(this._serverNode);\r\n                    updateParameters.timeoutInMs = 0;\r\n                    updateParameters.forceUpdate = true;\r\n                    updateParameters.debugTag = \"watch-topology-change\";\r\n                    // noinspection ES6MissingAwait\r\n                    this._requestExecutor.updateTopology(updateParameters);\r\n                    continue;\r\n                }\r\n                if (!type) {\r\n                    continue;\r\n                }\r\n\r\n                switch (type) {\r\n                    case \"Error\": {\r\n                        const exceptionAsString = message.Exception;\r\n                        this._notifyAboutError(exceptionAsString);\r\n                        break;\r\n                    }\r\n                    case \"Confirm\": {\r\n                        const commandId = message.CommandId;\r\n                        const confirmationResolver = this._confirmations.get(commandId);\r\n                        if (confirmationResolver) {\r\n                            confirmationResolver.resolve();\r\n                            this._confirmations.delete(commandId);\r\n                        }\r\n                        break;\r\n                    }\r\n                    default: {\r\n                        const value = message.Value;\r\n                        let transformedValue = ObjectUtil.transformObjectKeys(value, { defaultTransform: ObjectUtil.camel });\r\n                        if (type === \"TimeSeriesChange\") {\r\n                            const timeSeriesValue = transformedValue as ServerResponse<TimeSeriesChange>;\r\n\r\n                            const overrides: Partial<TimeSeriesChange> = {\r\n                                from: DateUtil.utc.parse(timeSeriesValue.from),\r\n                                to: DateUtil.utc.parse(timeSeriesValue.to)\r\n                            };\r\n\r\n                            transformedValue = Object.assign(transformedValue, overrides);\r\n                        }\r\n                        this._notifySubscribers(type, transformedValue);\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        } catch (err) {\r\n            this._notifyAboutError(err);\r\n            throwError(\"ChangeProcessingException\", \"There was an error during notification processing.\", err);\r\n        }\r\n    }\r\n\r\n    private _notifySubscribers(type: string, value: any): void {\r\n        switch (type) {\r\n            case \"AggressiveCacheChange\": {\r\n                for (const state of this._counters.values()) {\r\n                    state.send(\"AggressiveCache\", AggressiveCacheChange.INSTANCE);\r\n                }\r\n                break;\r\n            }\r\n            case \"DocumentChange\": {\r\n                for (const state of this._counters.values()) {\r\n                    state.send(\"Document\", value);\r\n                }\r\n                break;\r\n            }\r\n            case \"CounterChange\": {\r\n                for (const state of this._counters.values()) {\r\n                    state.send(\"Counter\", value);\r\n                }\r\n                break;\r\n            }\r\n            case \"TimeSeriesChange\": {\r\n                for (const state of this._counters.values()) {\r\n                    state.send(\"TimeSeries\", value);\r\n                }\r\n                break;\r\n            }\r\n            case \"IndexChange\": {\r\n                for (const state of this._counters.values()) {\r\n                    state.send(\"Index\", value);\r\n                }\r\n                break;\r\n            }\r\n            case \"OperationStatusChange\": {\r\n                for (const state of this._counters.values()) {\r\n                    state.send(\"Operation\", value);\r\n                }\r\n                break;\r\n            }\r\n            case \"TopologyChange\": {\r\n                const topologyChange = value as TopologyChange;\r\n                const requestExecutor = this._requestExecutor;\r\n                if (requestExecutor) {\r\n                    const node = new ServerNode({\r\n                        url: topologyChange.url,\r\n                        database: topologyChange.database\r\n                    });\r\n\r\n                    const updateParameters = new UpdateTopologyParameters(node);\r\n                    updateParameters.timeoutInMs = 0;\r\n                    updateParameters.forceUpdate = true;\r\n                    updateParameters.debugTag = \"topology-change-notification\";\r\n\r\n                    // noinspection JSIgnoredPromiseFromCall\r\n                    requestExecutor.updateTopology(updateParameters);\r\n                }\r\n                break;\r\n            }\r\n            default: {\r\n                throwError(\"NotSupportedException\");\r\n            }\r\n        }\r\n    }\r\n\r\n    private _notifyAboutError(e: Error): void {\r\n        if (this._isCanceled) {\r\n            return;\r\n        }\r\n\r\n        this._emitter.emit(\"error\", e);\r\n\r\n        for (const state of this._counters.values()) {\r\n            state.error(e);\r\n        }\r\n    }\r\n\r\n    public forAllCounters(): IChangesObservable<CounterChange> {\r\n        const counter = this._getOrAddConnectionState(\"all-counters\", \"watch-counters\", \"unwatch-counters\", null);\r\n        const taskedObservable = new ChangesObservable<CounterChange, DatabaseConnectionState>(\r\n            \"Counter\", counter, notification => true);\r\n        return taskedObservable;\r\n    }\r\n\r\n    public forCounter(counterName: string): IChangesObservable<CounterChange> {\r\n        if (StringUtil.isNullOrWhitespace(counterName)) {\r\n            throwError(\"InvalidArgumentException\", \"CounterName cannot be null or whitespace.\");\r\n        }\r\n\r\n        const counter = this._getOrAddConnectionState(\r\n            \"counter/\" + counterName, \"watch-counter\", \"unwatch-counter\", counterName);\r\n        const taskedObservable = new ChangesObservable<CounterChange, DatabaseConnectionState>(\r\n                \"Counter\", counter, notification => StringUtil.equalsIgnoreCase(counterName, notification.name));\r\n        return taskedObservable;\r\n    }\r\n\r\n    public forCounterOfDocument(documentId: string, counterName: string): IChangesObservable<CounterChange> {\r\n        if (StringUtil.isNullOrWhitespace(documentId)) {\r\n            throwError(\"InvalidArgumentException\", \"DocumentId cannot be null or whitespace.\");\r\n        }\r\n\r\n        if (StringUtil.isNullOrWhitespace(counterName)) {\r\n            throwError(\"InvalidArgumentException\", \"CounterName cannot be null or whitespace.\");\r\n        }\r\n\r\n        const counter = this._getOrAddConnectionState(\r\n            \"document/\" + documentId + \"/counter/\" + counterName,\r\n            \"watch-document-counter\",\r\n            \"unwatch-document-counter\",\r\n            null,\r\n            [ documentId, counterName ]);\r\n        const taskedObservable = new ChangesObservable<CounterChange, DatabaseConnectionState>(\r\n            \"Counter\", counter,\r\n            notification => StringUtil.equalsIgnoreCase(documentId, notification.documentId)\r\n                && StringUtil.equalsIgnoreCase(counterName, notification.name));\r\n        return taskedObservable;\r\n    }\r\n\r\n    public forCountersOfDocument(documentId: string): IChangesObservable<CounterChange> {\r\n        if (StringUtil.isNullOrWhitespace(documentId)) {\r\n            throwError(\r\n                \"InvalidArgumentException\",\r\n                \"DocumentId cannot be null or whitespace.\");\r\n        }\r\n\r\n        const counter = this._getOrAddConnectionState(\r\n            \"document/\" + documentId + \"/counter\", \"watch-document-counters\", \"unwatch-document-counters\", documentId);\r\n        const taskedObservable = new ChangesObservable<CounterChange, DatabaseConnectionState>(\r\n            \"Counter\",\r\n            counter,\r\n            notification => StringUtil.equalsIgnoreCase(documentId, notification.documentId));\r\n        return taskedObservable;\r\n    }\r\n\r\n    public forAllTimeSeries(): IChangesObservable<TimeSeriesChange> {\r\n        const counter = this._getOrAddConnectionState(\r\n            \"all-timeseries\", \"watch-all-timeseries\", \"unwatch-all-timeseries\", null);\r\n\r\n        const taskedObservable = new ChangesObservable<TimeSeriesChange, DatabaseConnectionState>(\r\n            \"TimeSeries\",\r\n            counter,\r\n            () => true\r\n        );\r\n\r\n        return taskedObservable;\r\n    }\r\n\r\n    public forTimeSeries(timeSeriesName: string): IChangesObservable<TimeSeriesChange> {\r\n        if (StringUtil.isNullOrWhitespace(timeSeriesName)) {\r\n            throwError(\"InvalidArgumentException\", \"TimeSeriesName cannot be null or whitespace.\");\r\n        }\r\n\r\n        const counter = this._getOrAddConnectionState(\r\n            \"timeseries/\" + timeSeriesName, \"watch-timeseries\", \"unwatch-timeseries\", timeSeriesName);\r\n\r\n        const taskedObservable = new ChangesObservable<TimeSeriesChange, DatabaseConnectionState>(\r\n            \"TimeSeries\",\r\n            counter,\r\n            notification => StringUtil.equalsIgnoreCase(timeSeriesName, notification.name));\r\n\r\n        return taskedObservable;\r\n    }\r\n\r\n    public forTimeSeriesOfDocument(documentId: string)\r\n    public forTimeSeriesOfDocument(documentId: string, timeSeriesName: string)\r\n    public forTimeSeriesOfDocument(documentId: string, timeSeriesName?: string) {\r\n        if (timeSeriesName) {\r\n            return this._forTimeSeriesOfDocumentWithNameInternal(documentId, timeSeriesName);\r\n        } else {\r\n            return this._forTimeSeriesOfDocumentInternal(documentId);\r\n        }\r\n    }\r\n\r\n    private _forTimeSeriesOfDocumentInternal(documentId: string) {\r\n        if (StringUtil.isNullOrWhitespace(documentId)) {\r\n            throwError(\"InvalidArgumentException\", \"DocumentId cannot be null or whitespace.\");\r\n        }\r\n\r\n        const counter = this._getOrAddConnectionState(\r\n            \"document/\" + documentId + \"/timeseries\", \"watch-all-document-timeseries\", \"unwatch-all-document-timeseries\", documentId);\r\n\r\n        const taskedObservable = new ChangesObservable<TimeSeriesChange, DatabaseConnectionState>(\r\n            \"TimeSeries\",\r\n            counter,\r\n            notification => StringUtil.equalsIgnoreCase(documentId, notification.documentId)\r\n        );\r\n\r\n        return taskedObservable;\r\n    }\r\n\r\n    private _forTimeSeriesOfDocumentWithNameInternal(documentId: string, timeSeriesName: string) {\r\n        if (StringUtil.isNullOrWhitespace(documentId)) {\r\n            throwError(\"InvalidArgumentException\", \"DocumentId cannot be null or whitespace.\");\r\n        }\r\n\r\n        if (StringUtil.isNullOrWhitespace(timeSeriesName)) {\r\n            throwError(\"InvalidArgumentException\", \"TimeSeriesName cannot be null or whitespace.\");\r\n        }\r\n\r\n        const counter = this._getOrAddConnectionState(\r\n            \"document/\" + documentId + \"/timeseries/\" + timeSeriesName,\r\n            \"watch-document-timeseries\",\r\n            \"unwatch-document-timeseries\", null, [ documentId, timeSeriesName ]);\r\n\r\n        const taskedObservable = new ChangesObservable<TimeSeriesChange, DatabaseConnectionState>(\r\n            \"TimeSeries\",\r\n            counter,\r\n            notification => StringUtil.equalsIgnoreCase(timeSeriesName, notification.name)\r\n                && StringUtil.equalsIgnoreCase(documentId, notification.documentId)\r\n        );\r\n\r\n        return taskedObservable;\r\n    }\r\n\r\n}\r\n"]}