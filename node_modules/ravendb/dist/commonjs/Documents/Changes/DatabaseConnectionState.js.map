{"version": 3, "file": "DatabaseConnectionState.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/DatabaseConnectionState.ts"], "names": [], "mappings": ";;;AAEA,6CAA2C;AAC3C,wDAAqD;AAErD,iEAAqD;AACrD,2DAAqD;AAErD,MAAa,uBAAuB;IAExB,MAAM,CAAU,WAAW,GAAG,OAAO,CAAC;IAEtC,QAAQ,GAAG,IAAI,0BAAY,EAAE,CAAC;IAE/B,UAAU,CAAC,OAA+B;QAC7C,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,uBAAuB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IAC5E,CAAC;IAEM,aAAa,CAAC,OAA+B;QAChD,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IAC/E,CAAC;IAEgB,aAAa,CAAsB;IACpC,SAAS,CAAsB;IAEvC,MAAM,GAAG,CAAC,CAAC;IACZ,SAAS,CAAQ;IAEP,SAAS,CAAe;IAEjC,UAAU,CAAgB;IAE3B,GAAG,CAAC,UAAyB;QAChC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAC9B,UAAU;iBACL,IAAI,CAAC,GAAG,EAAE;gBACP,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACtC,CAAC,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,EAAE;gBACX,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;QACX,CAAC;aAAM,CAAC;YACJ,UAAU,CAAC,KAAK,CAAC,sBAAQ,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IACjC,CAAC;IAEM,GAAG;QACN,IAAI,CAAC,MAAM,EAAE,CAAC;IAClB,CAAC;IAEM,GAAG;QACN,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QACnC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,CAAQ;QACjB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC/D,CAAC;IAEM,mBAAmB;QACtB,OAAO,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACtE,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,IAAA,mBAAQ,EAAC,2BAA2B,EACxD,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,UAAyB,CAAC,CAAC;QAC5D,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,OAAsB,CAAC,CAAC;QACzD,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,WAA0B,CAAC,CAAC;QAC7D,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,SAAwB,CAAC,CAAC;QAC3D,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,YAA2B,CAAC,CAAC;QAC9D,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;IAC1E,CAAC;IAED,YAAmB,SAA8B,EAAE,YAAiC;QAChF,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QAElC,IAAI,CAAC,SAAS,GAAG,IAAA,sBAAK,GAAQ,CAAC;IACnC,CAAC;IAEM,uBAAuB,CAAC,IAAiB,EAAE,OAAyC;QACvF,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC7C,CAAC;IAEM,0BAA0B,CAAC,IAAiB,EAAE,OAAyC;QAC1F,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAEM,IAAI,CAAC,IAAiB,EAAE,MAAsB;QACjD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC;;AA3FL,0DA4FC", "sourcesContent": ["import { DatabaseChange } from \"./DatabaseChange.js\";\r\nimport { ChangesType, IChangesConnectionState } from \"./IChangesConnectionState.js\";\r\nimport { EventEmitter } from \"node:events\";\r\nimport { getError } from \"../../Exceptions/index.js\";\r\nimport { IDefer } from \"../../Utility/PromiseUtil.js\";\r\nimport { defer } from \"../../Utility/PromiseUtil.js\";\r\nimport { TypeUtil } from \"../../Utility/TypeUtil.js\";\r\n\r\nexport class DatabaseConnectionState implements IChangesConnectionState<DatabaseChange> {\r\n\r\n    private static readonly ERROR_EVENT = \"error\";\r\n\r\n    private _emitter = new EventEmitter();\r\n\r\n    public addOnError(handler: (value: Error) => void): void {\r\n        this._emitter.addListener(DatabaseConnectionState.ERROR_EVENT, handler);\r\n    }\r\n\r\n    public removeOnError(handler: (value: Error) => void): void {\r\n        this._emitter.removeListener(DatabaseConnectionState.ERROR_EVENT, handler);\r\n    }\r\n\r\n    private readonly _onDisconnect: () => Promise<void>;\r\n    public readonly onConnect: () => Promise<void>;\r\n\r\n    private _value = 0;\r\n    public lastError: Error;\r\n\r\n    private readonly _firstSet: IDefer<void>;\r\n\r\n    private _connected: Promise<void>;\r\n\r\n    public set(connection: Promise<void>): void {\r\n        if (!this._firstSet.isFulfilled) {\r\n            connection\r\n                .then(() => {\r\n                    this._firstSet.resolve(undefined);\r\n                })\r\n                .catch(error => {\r\n                    this._firstSet.reject(error);\r\n                });\r\n        } else {\r\n            connection.catch(TypeUtil.NOOP);\r\n        }\r\n\r\n        this._connected = connection;\r\n    }\r\n\r\n    public inc(): void {\r\n        this._value++;\r\n    }\r\n\r\n    public dec(): void {\r\n        this._value--;\r\n        if (!this._value) {\r\n            this.set(this._onDisconnect());\r\n        }\r\n    }\r\n\r\n    public error(e: Error): void {\r\n        this.set(Promise.reject(e));\r\n        this.lastError = e;\r\n        this._emitter.emit(DatabaseConnectionState.ERROR_EVENT, e);\r\n    }\r\n\r\n    public ensureSubscribedNow(): Promise<void> {\r\n        return this._connected || Promise.resolve(this._firstSet.promise);\r\n    }\r\n\r\n    public dispose(): void {\r\n        this.set(Promise.reject(getError(\"InvalidOperationException\",\r\n            \"Object was disposed\")));\r\n        this._emitter.removeAllListeners(\"Document\" as ChangesType);\r\n        this._emitter.removeAllListeners(\"Index\" as ChangesType);\r\n        this._emitter.removeAllListeners(\"Operation\" as ChangesType);\r\n        this._emitter.removeAllListeners(\"Counter\" as ChangesType);\r\n        this._emitter.removeAllListeners(\"TimeSeries\" as ChangesType);\r\n        this._emitter.removeAllListeners(DatabaseConnectionState.ERROR_EVENT);\r\n    }\r\n\r\n    public constructor(onConnect: () => Promise<void>, onDisconnect: () => Promise<void>) {\r\n        this.onConnect = onConnect;\r\n        this._onDisconnect = onDisconnect;\r\n        this._value = 0;\r\n        this._emitter.setMaxListeners(50);\r\n\r\n        this._firstSet = defer<void>();\r\n    }\r\n\r\n    public addOnChangeNotification(type: ChangesType, handler: (change: DatabaseChange) => void): void {\r\n        this._emitter.addListener(type, handler);\r\n    }\r\n\r\n    public removeOnChangeNotification(type: ChangesType, handler: (change: DatabaseChange) => void): void {\r\n        this._emitter.removeListener(type, handler);\r\n    }\r\n\r\n    public send(type: ChangesType, change: DatabaseChange) {\r\n        this._emitter.emit(type, change);\r\n    }\r\n}\r\n"]}