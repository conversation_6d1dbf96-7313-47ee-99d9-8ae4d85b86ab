{"version": 3, "file": "IDocumentChanges.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/IDocumentChanges.ts"], "names": [], "mappings": "", "sourcesContent": ["import { IChangesObservable } from \"./IChangesObservable.js\";\r\nimport { ObjectTypeDescriptor } from \"../../Types/index.js\";\r\n\r\n\r\nexport interface IDocumentChanges<TChange> {\r\n    /**\r\n     * Subscribe to changes for specified document only.\r\n     */\r\n    forDocument(docId: string): IChangesObservable<TChange>;\r\n\r\n    /**\r\n     * Subscribe to changes for all documents.\r\n     */\r\n    forAllDocuments(): IChangesObservable<TChange>;\r\n\r\n    /**\r\n     * Subscribe to changes for all documents that Id starts with given prefix.\r\n     */\r\n    forDocumentsStartingWith(docIdPrefix: string): IChangesObservable<TChange>;\r\n\r\n    /**\r\n     * Subscribe to changes for all documents that belong to specified collection (Raven-Entity-Name).\r\n     */\r\n    forDocumentsInCollection(collectionName: string): IChangesObservable<TChange>;\r\n\r\n    /**\r\n     * Subscribe to changes for all documents that belong to specified collection (Raven-Entity-Name).\r\n     */\r\n    forDocumentsInCollection<T extends object>(type: ObjectTypeDescriptor<T>): IChangesObservable<TChange>;\r\n}"]}