{"version": 3, "file": "IConnectableChanges.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/IConnectableChanges.ts"], "names": [], "mappings": "", "sourcesContent": ["import { IDisposable } from \"../../Types/Contracts.js\";\r\n\r\nexport interface IConnectableChanges<T> extends IDisposable {\r\n\r\n    connected: boolean;\r\n\r\n    ensureConnectedNow(): Promise<T>;\r\n\r\n    on(type: \"connectionStatus\", handler: () => void): void;\r\n    on(type: \"error\", handler: (error: Error) => void): void;\r\n\r\n    off(type: \"connectionStatus\", handler: () => void): void;\r\n    off(type: \"error\", handler: (error: Error) => void): void;\r\n}\r\n"]}