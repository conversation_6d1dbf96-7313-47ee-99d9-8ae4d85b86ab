{"version": 3, "file": "ChangesObservable.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/ChangesObservable.ts"], "names": [], "mappings": ";;;AAGA,MAAa,iBAAiB;IAGT,KAAK,CAAc;IACnB,gBAAgB,CAAmB;IACnC,OAAO,CAAsB;IAC7B,YAAY,GAA4B,IAAI,GAAG,EAAE,CAAC;IAClD,iBAAiB,GAAgC,IAAI,GAAG,EAAE,CAAC;IAEpE,YAAY,CAAmB;IAC/B,aAAa,CAAuB;IAE5C,YAAmB,IAAiB,EAAE,eAAiC,EAAE,MAA2B;QAChG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAIM,EAAE,CAAC,KAAuB,EAAE,OAAwD;QACvF,QAAQ,KAAK,EAAE,CAAC;YACZ,KAAK,MAAM,CAAC,CAAC,CAAC;gBACV,yFAAyF;gBACzF,sCAAsC;gBACtC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;oBACrB,0BAA0B;oBAC1B,IAAI,CAAC,YAAY,GAAG,CAAC,OAAU,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACvD,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBACjF,CAAC;gBAED,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAA6B,CAAC,CAAC;gBACrD,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC;gBAC5B,MAAM;YACV,CAAC;YACD,KAAK,OAAO,CAAC,CAAC,CAAC;gBACX,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;oBACtB,gCAAgC;oBAChC,IAAI,CAAC,aAAa,GAAG,CAAC,EAAS,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;oBACnD,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACzD,CAAC;gBAED,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAiC,CAAC,CAAC;gBAC9D,MAAM;YACV,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAIM,cAAc,CAAC,KAAuB,EAAE,OAAwD;QACnG,OAAO,IAAI,CAAC,GAAG,CAAC,KAAY,EAAE,OAAc,CAAC,CAAC;IAClD,CAAC;IAIM,GAAG,CAAC,KAAuB,EAAE,OAAwD;QAExF,QAAQ,KAAK,EAAE,CAAC;YACZ,KAAK,MAAM,CAAC,CAAC,CAAC;gBACV,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAA6B,CAAC,EAAE,CAAC;oBAC1D,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC;gBAChC,CAAC;gBAED,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;oBAC1B,gDAAgD;oBAChD,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;oBAChF,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;gBAClC,CAAC;gBAED,MAAM;YACV,CAAC;YACD,KAAK,OAAO,CAAC,CAAC,CAAC;gBACX,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAiC,CAAC,CAAC;gBACjE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;oBAC/B,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBACxD,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;gBACnC,CAAC;gBACD,MAAM;YACV,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,IAAI,CAAC,GAAM;QACd,IAAI,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;gBACrB,OAAO;YACX,CAAC;QACL,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACd,OAAO;QACX,CAAC;QAED,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY;YAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC;IAEM,KAAK,CAAC,CAAQ;QACjB,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC;IACL,CAAC;IAEM,mBAAmB;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,CAAC;IACvD,CAAC;CACJ;AA7GD,8CA6GC", "sourcesContent": ["import { ChangesType, IChangesConnectionState } from \"./IChangesConnectionState.js\";\r\nimport { IChangesObservable } from \"./IChangesObservable.js\";\r\n\r\nexport class ChangesObservable<T, TConnectionState extends IChangesConnectionState<any>>\r\n    implements IChangesObservable<T> {\r\n\r\n    private readonly _type: ChangesType;\r\n    private readonly _connectionState: TConnectionState;\r\n    private readonly _filter: (val: T) => boolean;\r\n    private readonly _subscribers: Set<(value: T) => void> = new Set();\r\n    private readonly _errorSubscribers: Set<(error: Error) => void> = new Set();\r\n\r\n    private _sendHandler: (val: T) => void;\r\n    private _errorHandler: (val: Error) => void;\r\n\r\n    public constructor(type: ChangesType, connectionState: TConnectionState, filter: (val: T) => boolean) {\r\n        this._type = type;\r\n        this._connectionState = connectionState;\r\n        this._filter = filter;\r\n    }\r\n\r\n    public on(event: \"data\", handler: (value: T) => void): this;\r\n    public on(event: \"error\", handler: (error: Error) => void): this;\r\n    public on(event: \"data\" | \"error\", handler: ((value: T) => void) | ((error: Error) => void)): this {\r\n        switch (event) {\r\n            case \"data\": {\r\n                // since allow multiple subscriptions on single object we cant register it multiple times\r\n                // to avoid duplicates in notification\r\n                if (!this._sendHandler) {\r\n                    // register shared handler\r\n                    this._sendHandler = (payload: T) => this.send(payload);\r\n                    this._connectionState.addOnChangeNotification(this._type, this._sendHandler);\r\n                }\r\n\r\n                this._subscribers.add(handler as (value: T) => void);\r\n                this._connectionState.inc();\r\n                break;\r\n            }\r\n            case \"error\": {\r\n                if (!this._errorHandler) {\r\n                    // register shared error handler\r\n                    this._errorHandler = (ex: Error) => this.error(ex);\r\n                    this._connectionState.addOnError(this._errorHandler);\r\n                }\r\n\r\n                this._errorSubscribers.add(handler as (error: Error) => void);\r\n                break;\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    public removeListener(event: \"data\", handler: (value: T) => void): this;\r\n    public removeListener(event: \"error\", handler: (error: Error) => void): this;\r\n    public removeListener(event: \"data\" | \"error\", handler: ((value: T) => void) | ((error: Error) => void)): this {\r\n        return this.off(event as any, handler as any);\r\n    }\r\n\r\n    public off(event: \"data\", handler: (value: T) => void): this;\r\n    public off(event: \"error\", handler: (error: Error) => void): this;\r\n    public off(event: \"data\" | \"error\", handler: ((value: T) => void) | ((error: Error) => void)): this {\r\n\r\n        switch (event) {\r\n            case \"data\": {\r\n                if (this._subscribers.delete(handler as (value: T) => void)) {\r\n                    this._connectionState.dec();\r\n                }\r\n\r\n                if (!this._subscribers.size) {\r\n                    // no more subscribers left - remove from parent\r\n                    this._connectionState.removeOnChangeNotification(this._type, this._sendHandler);\r\n                    this._sendHandler = undefined;\r\n                }\r\n\r\n                break;\r\n            }\r\n            case \"error\": {\r\n                this._errorSubscribers.delete(handler as (error: Error) => void);\r\n                if (!this._errorSubscribers.size) {\r\n                    this._connectionState.removeOnError(this._errorHandler);\r\n                    this._errorHandler = undefined;\r\n                }\r\n                break;\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    public send(msg: T): void {\r\n        try {\r\n            if (!this._filter(msg)) {\r\n                return;\r\n            }\r\n        } catch (e) {\r\n            this.error(e);\r\n            return;\r\n        }\r\n\r\n        for (const x of this._subscribers) x(msg);\r\n    }\r\n\r\n    public error(e: Error): void {\r\n        for (const x of this._errorSubscribers) {\r\n            x(e);\r\n        }\r\n    }\r\n\r\n    public ensureSubscribedNow(): Promise<void> {\r\n        return this._connectionState.ensureSubscribedNow();\r\n    }\r\n}\r\n"]}