{"version": 3, "file": "IChangesObservable.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/IChangesObservable.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface IChangesObservable<T> extends IObservable<T> {\r\n    ensureSubscribedNow(): Promise<void>;\r\n}\r\n\r\nexport interface IObservable<T> {\r\n    on(event: \"data\", handler: (value: T) => void): this;\r\n\r\n    on(event: \"error\", handler: (error: Error) => void): this;\r\n\r\n    off(event: \"data\", handler: (value: T) => void): this;\r\n\r\n    off(event: \"error\", handler: (error: Error) => void): this;\r\n\r\n    removeListener(event: \"data\", handler: (value: T) => void): this;\r\n\r\n    removeListener(event: \"error\", handler: (error: Error) => void): this;\r\n}\r\n"]}