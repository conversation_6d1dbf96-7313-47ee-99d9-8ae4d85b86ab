{"version": 3, "file": "CounterChange.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/CounterChange.ts"], "names": [], "mappings": "", "sourcesContent": ["import { DatabaseChange } from \"./DatabaseChange.js\";\r\n\r\nexport type CounterChangeTypes = \"None\" | \"Put\" | \"Delete\" | \"Increment\";\r\n\r\nexport interface CounterChange extends DatabaseChange {\r\n    name: string;\r\n    value: number;\r\n    documentId: string;\r\n    collectionName: string;\r\n    changeVector: string;\r\n    type: CounterChangeTypes;\r\n}\r\n"]}