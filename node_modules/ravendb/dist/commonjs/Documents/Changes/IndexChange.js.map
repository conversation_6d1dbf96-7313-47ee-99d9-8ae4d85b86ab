{"version": 3, "file": "IndexChange.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/IndexChange.ts"], "names": [], "mappings": "", "sourcesContent": ["import { DatabaseChange } from \"./DatabaseChange.js\";\r\n\r\nexport interface IndexChange extends DatabaseChange {\r\n    type: IndexChangeTypes;\r\n    name: string;\r\n}\r\n\r\nexport interface TopologyChange extends DatabaseChange {\r\n    url: string;\r\n    database: string;\r\n}\r\n\r\nexport type IndexChangeTypes =\r\n    \"None\"\r\n    | \"BatchCompleted\"\r\n    | \"IndexAdded\"\r\n    | \"IndexRemoved\"\r\n    | \"IndexDemotedToIdle\"\r\n    | \"IndexPromotedFromIdle\"\r\n    | \"IndexDemotedToDisabled\"\r\n    | \"IndexMarkedAsErrored\"\r\n    | \"SideBySideReplace\"\r\n    | \"Renamed\"\r\n    | \"IndexPaused\"\r\n    | \"LockModeChanged\"\r\n    | \"PriorityChanged\"\r\n    | \"RollingIndexChanged\"\r\n    ;\r\n"]}