{"version": 3, "file": "IIndexChanges.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/IIndexChanges.ts"], "names": [], "mappings": "", "sourcesContent": ["import { IChangesObservable } from \"./IChangesObservable.js\";\r\n\r\n\r\nexport interface IIndexChanges<TChange> {\r\n    /**\r\n     * Subscribe to changes for specified index only.\r\n     */\r\n    forIndex(indexName: string): IChangesObservable<TChange>;\r\n\r\n    /**\r\n     * Subscribe to changes for all indexes.\r\n     */\r\n    forAllIndexes(): IChangesObservable<TChange>;\r\n}"]}