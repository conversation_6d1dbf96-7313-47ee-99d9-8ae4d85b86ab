{"version": 3, "file": "ICounterChanges.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Changes/ICounterChanges.ts"], "names": [], "mappings": "", "sourcesContent": ["import { IChangesObservable } from \"./IChangesObservable.js\";\r\n\r\nexport interface ICounterChanges<TChanges> {\r\n    /**\r\n     * Subscribe for changes for all counters.\r\n     */\r\n    forAllCounters(): IChangesObservable<TChanges>;\r\n\r\n    /**\r\n     * Subscribe to changes for all counters with a given name.\r\n     */\r\n    forCounter(counterName: string): IChangesObservable<TChanges>;\r\n\r\n    /**\r\n     * Subscribe to changes for counter from a given document and with given name.\r\n     */\r\n    forCounterOfDocument(documentId: string, counterName: string): IChangesObservable<TChanges>;\r\n\r\n    /**\r\n     * Subscribe to changes for all counters from a given document.\r\n     */\r\n    forCountersOfDocument(documentId: string): IChangesObservable<TChanges>;\r\n}"]}