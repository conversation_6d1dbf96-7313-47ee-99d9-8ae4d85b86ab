import { ICommandData, CommandType } from "../CommandData.js";
import { DocumentCountersOperation } from "../../Operations/Counters/DocumentCountersOperation.js";
import { CounterOperation } from "../../Operations/Counters/CounterOperation.js";
export declare class CountersBatchCommandData implements ICommandData {
    private readonly _id;
    private readonly _name;
    private readonly _changeVector;
    private _fromEtl;
    private readonly _counters;
    constructor(documentId: string, counterOperation: CounterOperation);
    constructor(documentId: string, counterOperations: CounterOperation[]);
    get id(): string;
    get name(): string;
    get changeVector(): string;
    get fromEtl(): boolean;
    get counters(): DocumentCountersOperation;
    get type(): CommandType;
    hasDelete(counterName: string): boolean;
    hasIncrement(counterName: string): boolean;
    private _hasOperationType;
    serialize(): object;
}
//# sourceMappingURL=CountersBatchCommandData.d.ts.map