import { ICommandData, CommandType } from "../CommandData.js";
import { DocumentConventions } from "../../Conventions/DocumentConventions.js";
export declare class DeleteCompareExchangeCommandData implements ICommandData {
    private readonly _index;
    readonly id: string;
    changeVector: string;
    name: string;
    constructor(key: string, index: number);
    get type(): CommandType;
    serialize(conventions: DocumentConventions): object;
}
//# sourceMappingURL=DeleteCompareExchangeCommandData.d.ts.map