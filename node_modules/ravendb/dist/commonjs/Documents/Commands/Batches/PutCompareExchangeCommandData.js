"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PutCompareExchangeCommandData = void 0;
class PutCompareExchangeCommandData {
    _index;
    _document;
    id;
    changeVector;
    name;
    constructor(key, value, index) {
        this.id = key;
        this._document = value;
        this._index = index;
    }
    get type() {
        return "CompareExchangePUT";
    }
    serialize(conventions) {
        return {
            Id: this.id,
            Document: this._document,
            Index: this._index,
            Type: "CompareExchangePUT"
        };
    }
}
exports.PutCompareExchangeCommandData = PutCompareExchangeCommandData;
//# sourceMappingURL=PutCompareExchangeCommandData.js.map