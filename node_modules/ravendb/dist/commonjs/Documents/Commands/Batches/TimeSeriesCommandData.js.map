{"version": 3, "file": "TimeSeriesCommandData.js", "sourceRoot": "", "sources": ["../../../../../src/Documents/Commands/Batches/TimeSeriesCommandData.ts"], "names": [], "mappings": ";;;AACA,+FAAyF;AACzF,2DAA0D;AAK1D,MAAsB,qBAAqB;IAC/B,GAAG,CAAS;IACZ,KAAK,CAAS;IACd,WAAW,CAAsB;IAEzC,YAAmB,UAAkB,EAAE,IAAY;QAC/C,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,IAAA,qBAAU,EAAC,0BAA0B,EAAE,2BAA2B,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,IAAA,qBAAU,EAAC,0BAA0B,EAAE,qBAAqB,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,GAAG,GAAG,UAAU,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,WAAW,GAAG,IAAI,4CAAmB,EAAE,CAAC;QAC7C,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC;IACjC,CAAC;IAED,IAAW,EAAE;QACT,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB,CAAC;IAED,IAAW,EAAE,CAAC,KAAa;QACvB,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC;IACrB,CAAC;IAED,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAa;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAED,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC;IAChB,CAAC;IAID,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,SAAS,CAAC,WAAgC;QACtC,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,GAAG;YACZ,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC;YACnD,IAAI,EAAE,YAAY;SACrB,CAAA;IACL,CAAC;IAED,mBAAmB,CAAC,OAA0C;QAC1D,kBAAkB;IACtB,CAAC;CACJ;AA1DD,sDA0DC", "sourcesContent": ["import { CommandType, ICommandData } from \"../CommandData.js\";\r\nimport { TimeSeriesOperation } from \"../../Operations/TimeSeries/TimeSeriesOperation.js\";\r\nimport { throwError } from \"../../../Exceptions/index.js\";\r\nimport { DocumentConventions } from \"../../Conventions/DocumentConventions.js\";\r\nimport { InMemoryDocumentSessionOperations } from \"../../Session/InMemoryDocumentSessionOperations.js\";\r\n\r\n\r\nexport abstract class TimeSeriesCommandData implements ICommandData {\r\n    private _id: string;\r\n    private _name: string;\r\n    private _timeSeries: TimeSeriesOperation;\r\n\r\n    public constructor(documentId: string, name: string) {\r\n        if (!documentId) {\r\n            throwError(\"InvalidArgumentException\", \"DocumentId cannot be null\");\r\n        }\r\n\r\n        if (!name) {\r\n            throwError(\"InvalidArgumentException\", \"Name cannot be null\");\r\n        }\r\n\r\n        this._id = documentId;\r\n        this._name = name;\r\n\r\n        this._timeSeries = new TimeSeriesOperation();\r\n        this._timeSeries.name = name;\r\n    }\r\n\r\n    public get id(): string {\r\n        return this._id;\r\n    }\r\n\r\n    public set id(value: string) {\r\n        this._id = value;\r\n    }\r\n\r\n    public get name(): string {\r\n        return this._name;\r\n    }\r\n\r\n    public set name(value: string) {\r\n        this._name = value;\r\n    }\r\n\r\n    public get changeVector() {\r\n        return null;\r\n    }\r\n\r\n    public abstract get type(): CommandType;\r\n\r\n    public get timeSeries(): TimeSeriesOperation {\r\n        return this._timeSeries;\r\n    }\r\n\r\n    serialize(conventions: DocumentConventions): object {\r\n        return {\r\n            Id: this._id,\r\n            TimeSeries: this._timeSeries.serialize(conventions),\r\n            Type: \"TimeSeries\"\r\n        }\r\n    }\r\n\r\n    onBeforeSaveChanges(session: InMemoryDocumentSessionOperations) {\r\n        // empty by design\r\n    }\r\n}\r\n"]}