import { ICommandData, CommandType } from "../CommandData.js";
import { DocumentConventions } from "../../Conventions/DocumentConventions.js";
export declare class DeleteAttachmentCommandData implements ICommandData {
    readonly id: string;
    readonly name: string;
    readonly changeVector: string;
    readonly type: CommandType;
    constructor(documentId: string, name: string, changeVector: string);
    serialize(conventions: DocumentConventions): object;
}
//# sourceMappingURL=DeleteAttachmentCommandData.d.ts.map