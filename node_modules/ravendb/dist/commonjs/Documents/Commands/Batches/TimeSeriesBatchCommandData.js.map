{"version": 3, "file": "TimeSeriesBatchCommandData.js", "sourceRoot": "", "sources": ["../../../../../src/Documents/Commands/Batches/TimeSeriesBatchCommandData.ts"], "names": [], "mappings": ";;;AAEA,yEAAmE;AAEnE,MAAa,0BAA2B,SAAQ,gDAAqB;IAEjE,YAAmB,UAAkB,EAAE,IAAY,EAAE,OAA0B,EAAE,OAA0B;QACvG,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAExB,IAAI,OAAO,EAAE,CAAC;YACV,KAAK,MAAM,eAAe,IAAI,OAAO,EAAE,CAAC;gBACpC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAC5C,CAAC;QACL,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACV,KAAK,MAAM,eAAe,IAAI,OAAO,EAAE,CAAC;gBACpC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAC5C,CAAC;QACL,CAAC;IACL,CAAC;IAED,IAAW,IAAI;QACX,OAAO,YAAY,CAAC;IACxB,CAAC;CACJ;AArBD,gEAqBC", "sourcesContent": ["import { CommandType } from \"../CommandData.js\";\r\nimport { AppendOperation, DeleteOperation } from \"../../Operations/TimeSeries/TimeSeriesOperation.js\";\r\nimport { TimeSeriesCommandData } from \"./TimeSeriesCommandData.js\";\r\n\r\nexport class TimeSeriesBatchCommandData extends TimeSeriesCommandData {\r\n\r\n    public constructor(documentId: string, name: string, appends: AppendOperation[], deletes: DeleteOperation[]) {\r\n        super(documentId, name);\r\n\r\n        if (appends) {\r\n            for (const appendOperation of appends) {\r\n                this.timeSeries.append(appendOperation);\r\n            }\r\n        }\r\n\r\n        if (deletes) {\r\n            for (const deleteOperation of deletes) {\r\n                this.timeSeries.delete(deleteOperation);\r\n            }\r\n        }\r\n    }\r\n\r\n    public get type(): CommandType {\r\n        return \"TimeSeries\";\r\n    }\r\n}\r\n"]}