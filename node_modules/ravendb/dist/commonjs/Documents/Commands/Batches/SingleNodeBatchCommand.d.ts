import { RavenCommand } from "../../../Http/RavenCommand.js";
import { BatchCommandResult } from "../../Session/Operations/BatchCommandResult.js";
import { IDisposable } from "../../../Types/Contracts.js";
import { DocumentConventions } from "../../Conventions/DocumentConventions.js";
import { ICommandData } from "../CommandData.js";
import { BatchOptions } from "./BatchOptions.js";
import { TransactionMode } from "../../Session/TransactionMode.js";
import { HttpRequestParameters, HttpResponse } from "../../../Primitives/Http.js";
import { ServerNode } from "../../../Http/ServerNode.js";
import { Readable, Stream } from "node:stream";
import { Dispatcher } from "undici-types";
import { IndexBatchOptions, ReplicationBatchOptions } from "../../Session/IAdvancedSessionOperations.js";
import { ShardedBatchOptions } from "./ShardedBatchOptions.js";
export declare class SingleNodeBatchCommand extends RavenCommand<BatchCommandResult> implements IDisposable {
    private _supportsAtomicWrites;
    private readonly _attachmentStreams;
    private readonly _conventions;
    private readonly _commands;
    private readonly _options;
    private readonly _mode;
    constructor(conventions: DocumentConventions, commands: ICommandData[]);
    constructor(conventions: DocumentConventions, commands: ICommandData[], options: BatchOptions);
    constructor(conventions: DocumentConventions, commands: ICommandData[], options: BatchOptions, transactionMode: TransactionMode);
    send(agent: Dispatcher, requestOptions: HttpRequestParameters): Promise<{
        response: HttpResponse;
        bodyStream: Readable;
    }>;
    createRequest(node: ServerNode): HttpRequestParameters;
    setResponseAsync(bodyStream: Stream, fromCache: boolean): Promise<string>;
    protected _appendOptions(): string;
    protected static appendOptions(indexOptions: IndexBatchOptions, replicationOptions: ReplicationBatchOptions, shardedOptions: ShardedBatchOptions): string;
    get isReadRequest(): boolean;
    dispose(): void;
}
//# sourceMappingURL=SingleNodeBatchCommand.d.ts.map