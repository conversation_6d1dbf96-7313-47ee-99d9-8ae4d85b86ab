"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.forBehavior = forBehavior;
function forBehavior(behavior) {
    switch (behavior) {
        case "Default":
            return null;
        case "TransactionalSingleBucketOnly":
            return {
                batchBehavior: "TransactionalSingleBucketOnly"
            };
        case "NonTransactionalMultiBucket":
            return {
                batchBehavior: "NonTransactionalMultiBucket"
            };
    }
}
//# sourceMappingURL=ShardedBatchOptions.js.map