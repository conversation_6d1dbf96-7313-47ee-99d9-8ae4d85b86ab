import { ICommandData, CommandType } from "../CommandData.js";
import { DocumentConventions } from "../../Conventions/DocumentConventions.js";
export declare class PutCompareExchangeCommandData implements ICommandData {
    private readonly _index;
    private readonly _document;
    id: string;
    changeVector: string;
    name: string;
    constructor(key: string, value: object, index: number);
    get type(): CommandType;
    serialize(conventions: DocumentConventions): object;
}
//# sourceMappingURL=PutCompareExchangeCommandData.d.ts.map