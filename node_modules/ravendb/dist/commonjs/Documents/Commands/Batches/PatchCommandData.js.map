{"version": 3, "file": "PatchCommandData.js", "sourceRoot": "", "sources": ["../../../../../src/Documents/Commands/Batches/PatchCommandData.ts"], "names": [], "mappings": ";;;AAEA,2DAA0D;AAC1D,8DAAwD;AAIxD,MAAa,gBAAgB;IAClB,EAAE,CAAS;IACX,IAAI,GAAW,IAAI,CAAC;IACpB,eAAe,CAAM;IACrB,YAAY,CAAS;IACrB,KAAK,CAAe;IACpB,cAAc,CAAe;IAC7B,IAAI,GAAgB,OAAO,CAAC;IAC5B,cAAc,CAAU;IAE/B,YAAY,EAAU,EAAE,YAAoB,EAAE,KAAmB,EAAE,cAA6B;QAC5F,IAAI,CAAC,EAAE,EAAE,CAAC;YACN,IAAA,qBAAU,EAAC,0BAA0B,EAAE,mBAAmB,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,IAAA,qBAAU,EAAC,0BAA0B,EAAE,sBAAsB,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACzC,CAAC;IAEM,SAAS,CAAC,WAAgC;QAC7C,MAAM,MAAM,GAAG;YACX,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,IAAI,EAAE,OAAsB;YAC5B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC;YACxC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS;SAC/F,CAAC;QAEF,IAAI,CAAC,sBAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;YACpD,MAAM,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,sBAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;YACnD,MAAM,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;QACnD,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEM,mBAAmB,CAAC,OAA0C;QACjE,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACpD,CAAC;CACJ;AAhDD,4CAgDC", "sourcesContent": ["import { CommandType, ICommandData } from \"../CommandData.js\";\r\nimport { PatchRequest } from \"../../Operations/PatchRequest.js\";\r\nimport { throwError } from \"../../../Exceptions/index.js\";\r\nimport { TypeUtil } from \"../../../Utility/TypeUtil.js\";\r\nimport { InMemoryDocumentSessionOperations } from \"../../Session/InMemoryDocumentSessionOperations.js\";\r\nimport { DocumentConventions } from \"../../Conventions/DocumentConventions.js\";\r\n\r\nexport class PatchCommandData implements ICommandData {\r\n    public id: string;\r\n    public name: string = null;\r\n    public createIfMissing: any;\r\n    public changeVector: string;\r\n    public patch: PatchRequest;\r\n    public patchIfMissing: PatchRequest;\r\n    public type: CommandType = \"PATCH\";\r\n    public returnDocument: boolean;\r\n\r\n    constructor(id: string, changeVector: string, patch: PatchRequest, patchIfMissing?: PatchRequest) {\r\n        if (!id) {\r\n            throwError(\"InvalidArgumentException\", \"Id cannot be null\");\r\n        }\r\n\r\n        if (!patch) {\r\n            throwError(\"InvalidArgumentException\", \"Patch cannot be null\");\r\n        }\r\n\r\n        this.id = id;\r\n        this.patch = patch;\r\n        this.changeVector = changeVector;\r\n        this.patchIfMissing = patchIfMissing;\r\n    }\r\n\r\n    public serialize(conventions: DocumentConventions): object {\r\n        const result = {\r\n            Id: this.id,\r\n            ChangeVector: this.changeVector,\r\n            Type: \"PATCH\" as CommandType,\r\n            Patch: this.patch.serialize(conventions),\r\n            PatchIfMissing: this.patchIfMissing ? this.patchIfMissing.serialize(conventions) : undefined\r\n        };\r\n\r\n        if (!TypeUtil.isNullOrUndefined(this.createIfMissing)) {\r\n            result[\"CreateIfMissing\"] = this.createIfMissing;\r\n        }\r\n\r\n        if (!TypeUtil.isNullOrUndefined(this.returnDocument)) {\r\n            result[\"ReturnDocument\"] = this.returnDocument;\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    public onBeforeSaveChanges(session: InMemoryDocumentSessionOperations): void {\r\n        this.returnDocument = session.isLoaded(this.id);\r\n    }\r\n}\r\n"]}