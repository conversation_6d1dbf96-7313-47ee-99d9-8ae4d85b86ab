import { CommandType, ICommandData } from "../CommandData.js";
import { DocumentConventions } from "../../Conventions/DocumentConventions.js";
export declare class ForceRevisionCommandData implements ICommandData {
    id: string;
    name: string;
    changeVector: string;
    type: CommandType;
    constructor(id: string);
    serialize(conventions: DocumentConventions): object;
}
//# sourceMappingURL=ForceRevisionCommandData.d.ts.map