{"version": 3, "file": "CopyTimeSeriesCommandData.js", "sourceRoot": "", "sources": ["../../../../../src/Documents/Commands/Batches/CopyTimeSeriesCommandData.ts"], "names": [], "mappings": ";;;AACA,kEAA4D;AAC5D,2DAA0D;AAG1D,8DAAwD;AAExD,MAAa,yBAAyB;IAClB,EAAE,CAAS;IACX,IAAI,CAAS;IACtB,YAAY,CAAS;IACZ,aAAa,CAAS;IACtB,eAAe,CAAS;IACxB,IAAI,CAAQ;IACZ,EAAE,CAAQ;IAE1B,IAAW,IAAI;QACX,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED,YAAmB,gBAAwB,EAAE,UAAkB,EAAE,qBAA6B,EAAE,eAAuB,EACpG,IAAW,EAAE,EAAS;QACrC,IAAI,0BAAU,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAClD,IAAA,qBAAU,EAAC,0BAA0B,EAAE,gDAAgD,CAAC,CAAC;QAC7F,CAAC;QACD,IAAI,0BAAU,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5C,IAAA,qBAAU,EAAC,0BAA0B,EAAE,0CAA0C,CAAC,CAAC;QACvF,CAAC;QACD,IAAI,0BAAU,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,EAAE,CAAC;YACvD,IAAA,qBAAU,EAAC,0BAA0B,EAAE,qDAAqD,CAAC,CAAC;QAClG,CAAC;QACD,IAAI,0BAAU,CAAC,kBAAkB,CAAC,eAAe,CAAC,EAAE,CAAC;YACjD,IAAA,qBAAU,EAAC,0BAA0B,EAAE,+CAA+C,CAAC,CAAC;QAC5F,CAAC;QAED,IAAI,CAAC,EAAE,GAAG,gBAAgB,CAAC;QAC3B,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,qBAAqB,CAAC;QAC3C,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACjB,CAAC;IAED,SAAS,CAAC,WAAgC;QACtC,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI;YAC1D,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,sBAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI;YACpD,IAAI,EAAE,gBAAgB;SACzB,CAAA;IACL,CAAC;IAED,mBAAmB,CAAC,OAA0C;QAC1D,QAAQ;IACZ,CAAC;CACJ;AAnDD,8DAmDC", "sourcesContent": ["import { CommandType, ICommandData } from \"../CommandData.js\";\r\nimport { StringUtil } from \"../../../Utility/StringUtil.js\";\r\nimport { throwError } from \"../../../Exceptions/index.js\";\r\nimport { DocumentConventions } from \"../../Conventions/DocumentConventions.js\";\r\nimport { InMemoryDocumentSessionOperations } from \"../../Session/InMemoryDocumentSessionOperations.js\";\r\nimport { DateUtil } from \"../../../Utility/DateUtil.js\";\r\n\r\nexport class CopyTimeSeriesCommandData implements ICommandData {\r\n    public readonly id: string;\r\n    public readonly name: string;\r\n    public changeVector: string;\r\n    public readonly destinationId: string;\r\n    public readonly destinationName: string;\r\n    public readonly from?: Date;\r\n    public readonly to?: Date;\r\n\r\n    public get type(): CommandType {\r\n        return \"TimeSeriesCopy\";\r\n    }\r\n\r\n    public constructor(sourceDocumentId: string, sourceName: string, destinationDocumentId: string, destinationName: string,\r\n                       from?: Date, to?: Date) {\r\n        if (StringUtil.isNullOrWhitespace(sourceDocumentId)) {\r\n            throwError(\"InvalidArgumentException\", \"SourceDocumentId cannot be null or whitespace.\");\r\n        }\r\n        if (StringUtil.isNullOrWhitespace(sourceName)) {\r\n            throwError(\"InvalidArgumentException\", \"SourceName cannot be null or whitespace.\");\r\n        }\r\n        if (StringUtil.isNullOrWhitespace(destinationDocumentId)) {\r\n            throwError(\"InvalidArgumentException\", \"DestinationDocumentId cannot be null or whitespace.\");\r\n        }\r\n        if (StringUtil.isNullOrWhitespace(destinationName)) {\r\n            throwError(\"InvalidArgumentException\", \"DestinationName cannot be null or whitespace.\");\r\n        }\r\n\r\n        this.id = sourceDocumentId;\r\n        this.name = sourceName;\r\n        this.destinationId = destinationDocumentId;\r\n        this.destinationName = destinationName;\r\n        this.from = from;\r\n        this.to = to;\r\n    }\r\n\r\n    serialize(conventions: DocumentConventions): object {\r\n        return {\r\n            Id: this.id,\r\n            Name: this.name,\r\n            DestinationId: this.destinationId,\r\n            DestinationName: this.destinationName,\r\n            From: this.from ? DateUtil.utc.stringify(this.from) : null,\r\n            To: this.to ? DateUtil.utc.stringify(this.to) : null,\r\n            Type: \"TimeSeriesCopy\"\r\n        }\r\n    }\r\n\r\n    onBeforeSaveChanges(session: InMemoryDocumentSessionOperations) {\r\n        // empty\r\n    }\r\n}"]}