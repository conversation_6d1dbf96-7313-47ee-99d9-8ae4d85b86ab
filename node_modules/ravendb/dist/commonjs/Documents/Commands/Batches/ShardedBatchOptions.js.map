{"version": 3, "file": "ShardedBatchOptions.js", "sourceRoot": "", "sources": ["../../../../../src/Documents/Commands/Batches/ShardedBatchOptions.ts"], "names": [], "mappings": ";;AAOA,kCAaC;AAbD,SAAgB,WAAW,CAAC,QAA8B;IACtD,QAAQ,QAAQ,EAAE,CAAC;QACf,KAAK,SAAS;YACV,OAAO,IAAI,CAAC;QAChB,KAAK,+BAA+B;YAChC,OAAO;gBACH,aAAa,EAAE,+BAA+B;aACjD,CAAA;QACL,KAAK,6BAA6B;YAC9B,OAAO;gBACH,aAAa,EAAE,6BAA6B;aAC/C,CAAA;IACT,CAAC;AACL,CAAC", "sourcesContent": ["import { ShardedBatchBehavior } from \"../../Session/ShardedBatchBehavior.js\";\r\n\r\n\r\nexport interface ShardedBatchOptions {\r\n    batchBehavior: ShardedBatchBehavior;\r\n}\r\n\r\nexport function forBehavior(behavior: ShardedBatchBehavior): ShardedBatchOptions {\r\n    switch (behavior) {\r\n        case \"Default\":\r\n            return null;\r\n        case \"TransactionalSingleBucketOnly\":\r\n            return {\r\n                batchBehavior: \"TransactionalSingleBucketOnly\"\r\n            }\r\n        case \"NonTransactionalMultiBucket\":\r\n            return {\r\n                batchBehavior: \"NonTransactionalMultiBucket\"\r\n            }\r\n    }\r\n}"]}