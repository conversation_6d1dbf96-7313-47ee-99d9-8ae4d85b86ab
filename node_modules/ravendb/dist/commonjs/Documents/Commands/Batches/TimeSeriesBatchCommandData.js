"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeSeriesBatchCommandData = void 0;
const TimeSeriesCommandData_js_1 = require("./TimeSeriesCommandData.js");
class TimeSeriesBatchCommandData extends TimeSeriesCommandData_js_1.TimeSeriesCommandData {
    constructor(documentId, name, appends, deletes) {
        super(documentId, name);
        if (appends) {
            for (const appendOperation of appends) {
                this.timeSeries.append(appendOperation);
            }
        }
        if (deletes) {
            for (const deleteOperation of deletes) {
                this.timeSeries.delete(deleteOperation);
            }
        }
    }
    get type() {
        return "TimeSeries";
    }
}
exports.TimeSeriesBatchCommandData = TimeSeriesBatchCommandData;
//# sourceMappingURL=TimeSeriesBatchCommandData.js.map