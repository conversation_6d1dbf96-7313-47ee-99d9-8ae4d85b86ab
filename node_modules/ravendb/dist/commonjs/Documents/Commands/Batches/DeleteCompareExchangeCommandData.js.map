{"version": 3, "file": "DeleteCompareExchangeCommandData.js", "sourceRoot": "", "sources": ["../../../../../src/Documents/Commands/Batches/DeleteCompareExchangeCommandData.ts"], "names": [], "mappings": ";;;AAGA,MAAa,gCAAgC;IACxB,MAAM,CAAS;IAChB,EAAE,CAAS;IACpB,YAAY,CAAS;IACrB,IAAI,CAAS;IAEnB,YAAmB,GAAW,EAAE,KAAa;QAC1C,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QACd,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAED,IAAW,IAAI;QACX,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAEM,SAAS,CAAC,WAAgC;QAC7C,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,IAAI,EAAE,uBAAsC;SAC/C,CAAC;IACN,CAAC;CACJ;AAtBD,4EAsBC", "sourcesContent": ["import { ICommandData, CommandType } from \"../CommandData.js\";\r\nimport { DocumentConventions } from \"../../Conventions/DocumentConventions.js\";\r\n\r\nexport class DeleteCompareExchangeCommandData implements ICommandData {\r\n    private readonly _index: number;\r\n    public readonly id: string;\r\n    public changeVector: string;\r\n    public name: string;\r\n\r\n     public constructor(key: string, index: number) {\r\n        this.id = key;\r\n        this._index = index;\r\n    }\r\n\r\n    public get type(): CommandType {\r\n        return \"CompareExchangePUT\";\r\n    }\r\n\r\n    public serialize(conventions: DocumentConventions): object {\r\n        return {\r\n            Id: this.id,\r\n            Index: this._index,\r\n            Type: \"CompareExchangeDELETE\" as CommandType\r\n        };\r\n    }\r\n}\r\n"]}