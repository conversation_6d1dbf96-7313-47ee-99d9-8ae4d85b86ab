import { CommandType, ICommandData } from "../CommandData.js";
import { PatchRequest } from "../../Operations/PatchRequest.js";
import { InMemoryDocumentSessionOperations } from "../../Session/InMemoryDocumentSessionOperations.js";
import { DocumentConventions } from "../../Conventions/DocumentConventions.js";
export declare class PatchCommandData implements ICommandData {
    id: string;
    name: string;
    createIfMissing: any;
    changeVector: string;
    patch: PatchRequest;
    patchIfMissing: PatchRequest;
    type: CommandType;
    returnDocument: boolean;
    constructor(id: string, changeVector: string, patch: PatchRequest, patchIfMissing?: PatchRequest);
    serialize(conventions: DocumentConventions): object;
    onBeforeSaveChanges(session: InMemoryDocumentSessionOperations): void;
}
//# sourceMappingURL=PatchCommandData.d.ts.map