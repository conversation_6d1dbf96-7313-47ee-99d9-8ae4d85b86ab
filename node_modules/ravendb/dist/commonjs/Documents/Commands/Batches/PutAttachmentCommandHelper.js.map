{"version": 3, "file": "PutAttachmentCommandHelper.js", "sourceRoot": "", "sources": ["../../../../../src/Documents/Commands/Batches/PutAttachmentCommandHelper.ts"], "names": [], "mappings": ";;;AAAA,2DAA0D;AAE1D,MAAa,0BAA0B;IAC5B,MAAM,CAAC,yBAAyB;QACnC,IAAA,qBAAU,EAAC,2BAA2B,EAClC,+EAA+E;cAC7E,sDAAsD,CAAC,CAAC;IAClE,CAAC;CACJ;AAND,gEAMC", "sourcesContent": ["import { throwError } from \"../../../Exceptions/index.js\";\r\n\r\nexport class PutAttachmentCommandHelper {\r\n    public static throwStreamWasAlreadyUsed(): void {\r\n        throwError(\"InvalidOperationException\",\r\n            \"It is forbidden to re-use the same InputStream for more than one attachment. \"\r\n            + \"Use a unique InputStream per put attachment command.\");\r\n    }\r\n}\r\n"]}