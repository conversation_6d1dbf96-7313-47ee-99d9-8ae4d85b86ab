{"version": 3, "file": "PutAttachmentCommandData.js", "sourceRoot": "", "sources": ["../../../../../src/Documents/Commands/Batches/PutAttachmentCommandData.ts"], "names": [], "mappings": ";;;AAEA,kEAA4D;AAC5D,2DAA0D;AAG1D,MAAa,wBAAwB;IAC1B,EAAE,CAAS;IACX,IAAI,CAAS;IACb,YAAY,CAAS;IACrB,IAAI,GAAgB,eAAe,CAAC;IACpC,WAAW,CAAS;IACpB,SAAS,CAAiB;IAEjC,YACI,UAAkB,EAClB,IAAY,EACZ,MAAsB,EACtB,WAAmB,EACnB,YAAoB;QAEpB,IAAI,0BAAU,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5C,IAAA,qBAAU,EAAC,0BAA0B,EAAE,4BAA4B,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,0BAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,IAAA,qBAAU,EAAC,0BAA0B,EAAE,sBAAsB,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACrC,CAAC;IAEM,SAAS,CAAC,WAAgC;QAC7C,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,IAAI,EAAE,eAA8B;YACpC,WAAW,EAAE,IAAI,CAAC,WAAW;SAChC,CAAC;IACN,CAAC;CACJ;AAvCD,4DAuCC", "sourcesContent": ["import { ICommandData, CommandType } from \"../CommandData.js\";\r\nimport { AttachmentData } from \"../../Attachments/index.js\";\r\nimport { StringUtil } from \"../../../Utility/StringUtil.js\";\r\nimport { throwError } from \"../../../Exceptions/index.js\";\r\nimport { DocumentConventions } from \"../../Conventions/DocumentConventions.js\";\r\n\r\nexport class PutAttachmentCommandData implements ICommandData {\r\n    public id: string;\r\n    public name: string;\r\n    public changeVector: string;\r\n    public type: CommandType = \"AttachmentPUT\";\r\n    public contentType: string;\r\n    public attStream: AttachmentData;\r\n\r\n    public constructor(\r\n        documentId: string,\r\n        name: string,\r\n        stream: AttachmentData,\r\n        contentType: string,\r\n        changeVector: string) {\r\n\r\n        if (StringUtil.isNullOrWhitespace(documentId)) {\r\n            throwError(\"InvalidArgumentException\", \"DocumentId cannot be null.\");\r\n        }\r\n\r\n        if (StringUtil.isNullOrWhitespace(name)) {\r\n            throwError(\"InvalidArgumentException\", \"Name cannot be null.\");\r\n        }\r\n\r\n        this.id = documentId;\r\n        this.name = name;\r\n        this.attStream = stream;\r\n        this.contentType = contentType;\r\n        this.changeVector = changeVector;\r\n    }\r\n\r\n    public serialize(conventions: DocumentConventions): object {\r\n        return {\r\n            Id: this.id,\r\n            Name: this.name,\r\n            ChangeVector: this.changeVector,\r\n            Type: \"AttachmentPUT\" as CommandType,\r\n            ContentType: this.contentType\r\n        };\r\n    }\r\n}\r\n"]}