import { CommandType, ICommandData } from "../CommandData.js";
import { DocumentConventions } from "../../Conventions/DocumentConventions.js";
import { InMemoryDocumentSessionOperations } from "../../Session/InMemoryDocumentSessionOperations.js";
export declare class CopyTimeSeriesCommandData implements ICommandData {
    readonly id: string;
    readonly name: string;
    changeVector: string;
    readonly destinationId: string;
    readonly destinationName: string;
    readonly from?: Date;
    readonly to?: Date;
    get type(): CommandType;
    constructor(sourceDocumentId: string, sourceName: string, destinationDocumentId: string, destinationName: string, from?: Date, to?: Date);
    serialize(conventions: DocumentConventions): object;
    onBeforeSaveChanges(session: InMemoryDocumentSessionOperations): void;
}
//# sourceMappingURL=CopyTimeSeriesCommandData.d.ts.map