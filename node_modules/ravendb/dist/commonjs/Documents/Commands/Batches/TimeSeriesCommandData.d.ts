import { CommandType, ICommandData } from "../CommandData.js";
import { TimeSeriesOperation } from "../../Operations/TimeSeries/TimeSeriesOperation.js";
import { DocumentConventions } from "../../Conventions/DocumentConventions.js";
import { InMemoryDocumentSessionOperations } from "../../Session/InMemoryDocumentSessionOperations.js";
export declare abstract class TimeSeriesCommandData implements ICommandData {
    private _id;
    private _name;
    private _timeSeries;
    constructor(documentId: string, name: string);
    get id(): string;
    set id(value: string);
    get name(): string;
    set name(value: string);
    get changeVector(): any;
    abstract get type(): CommandType;
    get timeSeries(): TimeSeriesOperation;
    serialize(conventions: DocumentConventions): object;
    onBeforeSaveChanges(session: InMemoryDocumentSessionOperations): void;
}
//# sourceMappingURL=TimeSeriesCommandData.d.ts.map