{"version": 3, "file": "ForceRevisionCommandData.js", "sourceRoot": "", "sources": ["../../../../../src/Documents/Commands/Batches/ForceRevisionCommandData.ts"], "names": [], "mappings": ";;;AACA,2DAA0D;AAG1D,MAAa,wBAAwB;IAC1B,EAAE,CAAS;IACX,IAAI,CAAS;IACb,YAAY,CAAS;IACrB,IAAI,GAAgB,uBAAuB,CAAC;IAEnD,YAAmB,EAAU;QACzB,IAAI,CAAC,EAAE,EAAE,CAAC;YACN,IAAA,qBAAU,EAAC,0BAA0B,EAAE,mBAAmB,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACjB,CAAC;IAEM,SAAS,CAAC,WAAgC;QAC7C,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;SAClB,CAAA;IACL,CAAC;CACJ;AApBD,4DAoBC", "sourcesContent": ["import { CommandType, ICommandData } from \"../CommandData.js\";\r\nimport { throwError } from \"../../../Exceptions/index.js\";\r\nimport { DocumentConventions } from \"../../Conventions/DocumentConventions.js\";\r\n\r\nexport class ForceRevisionCommandData implements ICommandData {\r\n    public id: string;\r\n    public name: string;\r\n    public changeVector: string;\r\n    public type: CommandType = \"ForceRevisionCreation\";\r\n\r\n    public constructor(id: string) {\r\n        if (!id) {\r\n            throwError(\"InvalidArgumentException\", \"Id cannot be null\");\r\n        }\r\n\r\n        this.id = id;\r\n    }\r\n\r\n    public serialize(conventions: DocumentConventions): object {\r\n        return {\r\n            Id: this.id,\r\n            Type: this.type\r\n        }\r\n    }\r\n}\r\n"]}