"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PutAttachmentCommandData = void 0;
const StringUtil_js_1 = require("../../../Utility/StringUtil.js");
const index_js_1 = require("../../../Exceptions/index.js");
class PutAttachmentCommandData {
    id;
    name;
    changeVector;
    type = "AttachmentPUT";
    contentType;
    attStream;
    constructor(documentId, name, stream, contentType, changeVector) {
        if (StringUtil_js_1.StringUtil.isNullOrWhitespace(documentId)) {
            (0, index_js_1.throwError)("InvalidArgumentException", "DocumentId cannot be null.");
        }
        if (StringUtil_js_1.StringUtil.isNullOrWhitespace(name)) {
            (0, index_js_1.throwError)("InvalidArgumentException", "Name cannot be null.");
        }
        this.id = documentId;
        this.name = name;
        this.attStream = stream;
        this.contentType = contentType;
        this.changeVector = changeVector;
    }
    serialize(conventions) {
        return {
            Id: this.id,
            Name: this.name,
            ChangeVector: this.changeVector,
            Type: "AttachmentPUT",
            ContentType: this.contentType
        };
    }
}
exports.PutAttachmentCommandData = PutAttachmentCommandData;
//# sourceMappingURL=PutAttachmentCommandData.js.map