{"version": 3, "file": "PutCompareExchangeCommandData.js", "sourceRoot": "", "sources": ["../../../../../src/Documents/Commands/Batches/PutCompareExchangeCommandData.ts"], "names": [], "mappings": ";;;AAEA,MAAa,6BAA6B;IACrB,MAAM,CAAS;IACf,SAAS,CAAS;IAC5B,EAAE,CAAS;IACX,YAAY,CAAS;IACrB,IAAI,CAAS;IAEnB,YAAmB,GAAW,EAAE,KAAa,EAAE,KAAa;QACzD,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QACd,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAED,IAAW,IAAI;QACX,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAEM,SAAS,CAAC,WAAgC;QAC7C,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,IAAI,EAAE,oBAAmC;SAC5C,CAAC;IACN,CAAC;CACJ;AAzBD,sEAyBC", "sourcesContent": ["import { ICommandData, CommandType } from \"../CommandData.js\";\r\nimport { DocumentConventions } from \"../../Conventions/DocumentConventions.js\";\r\nexport class PutCompareExchangeCommandData implements ICommandData {\r\n    private readonly _index: number;\r\n    private readonly _document: object;\r\n    public id: string;\r\n    public changeVector: string;\r\n    public name: string;\r\n\r\n     public constructor(key: string, value: object, index: number) {\r\n        this.id = key;\r\n        this._document = value;\r\n        this._index = index;\r\n    }\r\n\r\n    public get type(): CommandType {\r\n        return \"CompareExchangePUT\";\r\n    }\r\n\r\n    public serialize(conventions: DocumentConventions): object {\r\n        return {\r\n            Id: this.id,\r\n            Document: this._document,\r\n            Index: this._index,\r\n            Type: \"CompareExchangePUT\" as CommandType\r\n        };\r\n    }\r\n}\r\n"]}