{"version": 3, "file": "DeleteAttachmentCommandData.js", "sourceRoot": "", "sources": ["../../../../../src/Documents/Commands/Batches/DeleteAttachmentCommandData.ts"], "names": [], "mappings": ";;;AACA,kEAA4D;AAC5D,2DAA0D;AAG1D,MAAa,2BAA2B;IACpB,EAAE,CAAS;IACX,IAAI,CAAS;IACb,YAAY,CAAS;IACrB,IAAI,GAAgB,kBAAkB,CAAC;IAEvD,YAAmB,UAAkB,EAAE,IAAY,EAAE,YAAoB;QACrE,IAAI,0BAAU,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5C,IAAA,qBAAU,EAAC,0BAA0B,EAAE,2BAA2B,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,0BAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,IAAA,qBAAU,EAAC,0BAA0B,EAAE,qBAAqB,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACrC,CAAC;IAEM,SAAS,CAAC,WAAgC;QAC7C,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,IAAI,EAAE,kBAAiC;SAC1C,CAAC;IACN,CAAC;CACJ;AA5BD,kEA4BC", "sourcesContent": ["import { ICommandData, CommandType } from \"../CommandData.js\";\r\nimport { StringUtil } from \"../../../Utility/StringUtil.js\";\r\nimport { throwError } from \"../../../Exceptions/index.js\";\r\nimport { DocumentConventions } from \"../../Conventions/DocumentConventions.js\";\r\n\r\nexport class DeleteAttachmentCommandData implements ICommandData {\r\n    public readonly id: string;\r\n    public readonly name: string;\r\n    public readonly changeVector: string;\r\n    public readonly type: CommandType = \"AttachmentDELETE\";\r\n\r\n    public constructor(documentId: string, name: string, changeVector: string) {\r\n        if (StringUtil.isNullOrWhitespace(documentId)) {\r\n            throwError(\"InvalidArgumentException\", \"DocumentId cannot be null\");\r\n        }\r\n\r\n        if (StringUtil.isNullOrWhitespace(name)) {\r\n            throwError(\"InvalidArgumentException\", \"Name cannot be null\");\r\n        }\r\n\r\n        this.id = documentId;\r\n        this.name = name;\r\n        this.changeVector = changeVector;\r\n    }\r\n\r\n    public serialize(conventions: DocumentConventions): object {\r\n        return {\r\n            Id: this.id,\r\n            Name: this.name,\r\n            ChangeVector: this.changeVector,\r\n            Type: \"AttachmentDELETE\" as CommandType\r\n        };\r\n    }\r\n}\r\n"]}