import { CommandType } from "../CommandData.js";
import { AppendOperation, DeleteOperation } from "../../Operations/TimeSeries/TimeSeriesOperation.js";
import { TimeSeriesCommandData } from "./TimeSeriesCommandData.js";
export declare class TimeSeriesBatchCommandData extends TimeSeriesCommandData {
    constructor(documentId: string, name: string, appends: AppendOperation[], deletes: DeleteOperation[]);
    get type(): CommandType;
}
//# sourceMappingURL=TimeSeriesBatchCommandData.d.ts.map