"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatchCommandData = void 0;
const index_js_1 = require("../../../Exceptions/index.js");
const TypeUtil_js_1 = require("../../../Utility/TypeUtil.js");
class PatchCommandData {
    id;
    name = null;
    createIfMissing;
    changeVector;
    patch;
    patchIfMissing;
    type = "PATCH";
    returnDocument;
    constructor(id, changeVector, patch, patchIfMissing) {
        if (!id) {
            (0, index_js_1.throwError)("InvalidArgumentException", "Id cannot be null");
        }
        if (!patch) {
            (0, index_js_1.throwError)("InvalidArgumentException", "Patch cannot be null");
        }
        this.id = id;
        this.patch = patch;
        this.changeVector = changeVector;
        this.patchIfMissing = patchIfMissing;
    }
    serialize(conventions) {
        const result = {
            Id: this.id,
            ChangeVector: this.changeVector,
            Type: "PATCH",
            Patch: this.patch.serialize(conventions),
            PatchIfMissing: this.patchIfMissing ? this.patchIfMissing.serialize(conventions) : undefined
        };
        if (!TypeUtil_js_1.TypeUtil.isNullOrUndefined(this.createIfMissing)) {
            result["CreateIfMissing"] = this.createIfMissing;
        }
        if (!TypeUtil_js_1.TypeUtil.isNullOrUndefined(this.returnDocument)) {
            result["ReturnDocument"] = this.returnDocument;
        }
        return result;
    }
    onBeforeSaveChanges(session) {
        this.returnDocument = session.isLoaded(this.id);
    }
}
exports.PatchCommandData = PatchCommandData;
//# sourceMappingURL=PatchCommandData.js.map