{"version": 3, "file": "SingleNodeBatchCommand.js", "sourceRoot": "", "sources": ["../../../../../src/Documents/Commands/Batches/SingleNodeBatchCommand.ts"], "names": [], "mappings": ";;;AAAA,mEAA6D;AAQ7D,2DAA0D;AAC1D,+EAAyE;AAEzE,8DAA8D;AAC9D,uEAAqE;AAErE,mGAA6F;AAC7F,6CAA+C;AAC/C,8DAAwD;AACxD,mFAA6E;AAC7E,8DAAwD;AACxD,kEAA4D;AAC5D,kEAA8D;AAK9D,MAAa,sBAAuB,SAAQ,8BAAgC;IAChE,qBAAqB,CAAiB;IAC7B,kBAAkB,CAAsB;IACxC,YAAY,CAAsB;IAClC,SAAS,CAAiB;IAC1B,QAAQ,CAAe;IACvB,KAAK,CAAkB;IASxC,YACI,WAAgC,EAChC,QAAwB,EACxB,UAAwB,IAAI,EAC5B,OAAwB,IAAI;QAC5B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,IAAA,qBAAU,EAAC,0BAA0B,EAAE,4BAA4B,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,IAAA,qBAAU,EAAC,0BAA0B,EAAE,yBAAyB,CAAC,CAAC;QACtE,CAAC;QAED,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnC,IAAI,OAAO,YAAY,sDAAwB,EAAE,CAAC;gBAC9C,MAAM,wBAAwB,GAAG,OAAmC,CAAC;gBACrE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,EAAE,CAAC;gBACxC,CAAC;gBAED,MAAM,EAAE,SAAS,EAAE,GAAG,wBAAwB,CAAC;gBAC/C,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;oBACzC,0DAA0B,CAAC,yBAAyB,EAAE,CAAC;gBAC3D,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBAC3C,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,KAAiB,EAAE,cAAqC;QAI/D,MAAM,EAAE,IAAI,EAAE,GAAG,cAAc,CAAC;QAChC,IAAI,IAAI,YAAY,QAAQ,EAAE,CAAC;YAC3B,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;iBAC3C,GAAG,CAAC,SAAS,CAAC,EAAE;gBACb,OAAO;oBACH,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE;wBACL,cAAc,EAAE,kBAAkB;qBACrC;iBACJ,CAAC;YACN,CAAC,CAAC,CAAC;YAEP,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC1C,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBACjC,MAAM,OAAO,GAAG,IAAI,YAAY,sBAAQ,CAAC,CAAC,CAAC,MAAM,IAAA,4BAAY,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC3E,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;YAC5C,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;IAC7C,CAAC;IAEM,aAAa,CAAC,IAAgB;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,aAAa,GAAG,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;QACrE,MAAM,OAAO,GAAG,4BAAc,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,EAAE,CAAC;QAE9D,IAAI,sBAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC;YACzD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,2BAA2B,CAAC;QAClE,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;YACzC,MAAM,UAAU,GAAG,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAClD,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC9B,OAAO,UAAU,CAAC,sBAAsB,CAAC,CAAC;YAC9C,CAAC;YAED,OAAO,UAAU,CAAC;QACtB,CAAC,CAAC,CAAA;QAEF,MAAM,IAAI,GAAG,8BAAc,CAAC,UAAU,EAAE,CAAC,SAAS,CAAC;YAC/C,QAAQ,EAAE,aAAa;YACvB,eAAe,EAAE,IAAI,CAAC,KAAK,KAAK,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;SAC5E,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,MAAM,OAAO,GAA0B;YACnC,MAAM,EAAE,MAAM;YACd,GAAG,EAAE,GAAG,GAAG,WAAW;SACzB,CAAC;QAEF,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC9D,4GAA4G;YAE5G,+HAA+H;YAC/H,IAAI,OAAO,CAAC,OAAO,IAAI,cAAc,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACvD,MAAM,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG,WAAW,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC;gBACxE,OAAO,CAAC,OAAO,GAAG,WAAW,CAAC;YAClC,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,QAAQ,EAAE,CAAC;YACjC,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC,CAAC;YAEzE,OAAO,CAAC,IAAI,GAAG,SAAS,CAAC;QAE7B,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;YACpB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC9B,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,UAAkB,EAAE,SAAkB;QAChE,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,IAAA,qBAAU,EAAC,2BAA2B,EAClC,wDAAwD;kBACtD,wDAAwD,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,IAAI,GAAW,IAAI,CAAC;QACxB,IAAI,CAAC,MAAM,GAAG,MAAM,8DAA4B,CAAC,MAAM,EAAsB;aACxE,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;aAC1B,aAAa,EAAE;aACf,mBAAmB,CAAC;YACjB,gBAAgB,EAAE,0BAAU,CAAC,KAAK;YAClC,UAAU,EAAE,CAAC,IAAI,CAAC;YAClB,WAAW,EAAE,CAAC,oCAAoC,CAAC;SACtD,CAAC;aACD,OAAO,CAAC,UAAU,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,cAAc;QACpB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,OAAO,EAAE,CAAC;QACd,CAAC;QAED,OAAO,sBAAsB,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;IAE5I,CAAC;IAES,MAAM,CAAC,aAAa,CAAC,YAA+B,EAAE,kBAA2C,EAAE,cAAmC;QAC5I,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,IAAI,kBAAkB,EAAE,CAAC;YACrB,MAAM,IAAI,2BAA2B,sBAAQ,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;YAE7F,MAAM,IAAI,mCAAmC,GAAG,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YAEvG,MAAM,IAAI,6BAA6B,CAAC;YACxC,MAAM,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,kBAAkB,CAAC,QAAQ,CAAC;QACrF,CAAC;QAED,IAAI,YAAY,EAAE,CAAC;YACf,MAAM,IAAI,yBAAyB,CAAC;YACpC,MAAM,IAAI,sBAAQ,CAAC,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAE1D,IAAI,YAAY,CAAC,cAAc,EAAE,CAAC;gBAC9B,MAAM,IAAI,yBAAyB,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,0BAA0B,CAAC;YACzC,CAAC;YAED,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;gBACvB,KAAK,MAAM,aAAa,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;oBAC/C,MAAM,IAAI,wBAAwB,GAAG,kBAAkB,CAAC,aAAa,CAAC,CAAC;gBAC3E,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,cAAc,EAAE,CAAC;YACjB,IAAI,cAAc,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;gBAC7C,MAAM,IAAI,wBAAwB,GAAG,cAAc,CAAC,aAAa,CAAC;YACtE,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,IAAW,aAAa;QACpB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,gEAAgE;IACzD,OAAO;QACV,QAAQ;IACZ,CAAC;CACJ;AA1MD,wDA0MC", "sourcesContent": ["import { RavenCommand } from \"../../../Http/RavenCommand.js\";\r\nimport { BatchCommandResult } from \"../../Session/Operations/BatchCommandResult.js\";\r\nimport { IDisposable } from \"../../../Types/Contracts.js\";\r\nimport { DocumentConventions } from \"../../Conventions/DocumentConventions.js\";\r\nimport { ICommandData } from \"../CommandData.js\";\r\nimport { AttachmentData } from \"../../Attachments/index.js\";\r\nimport { BatchOptions } from \"./BatchOptions.js\";\r\nimport { TransactionMode } from \"../../Session/TransactionMode.js\";\r\nimport { throwError } from \"../../../Exceptions/index.js\";\r\nimport { PutAttachmentCommandData } from \"./PutAttachmentCommandData.js\";\r\nimport { HttpRequestParameters, HttpResponse } from \"../../../Primitives/Http.js\";\r\nimport { HeadersBuilder } from \"../../../Utility/HttpUtil.js\";\r\nimport { JsonSerializer } from \"../../../Mapping/Json/Serializer.js\";\r\nimport { ServerNode } from \"../../../Http/ServerNode.js\";\r\nimport { RavenCommandResponsePipeline } from \"../../../Http/RavenCommandResponsePipeline.js\";\r\nimport { Readable, Stream } from \"node:stream\";\r\nimport { TimeUtil } from \"../../../Utility/TimeUtil.js\";\r\nimport { PutAttachmentCommandHelper } from \"./PutAttachmentCommandHelper.js\";\r\nimport { TypeUtil } from \"../../../Utility/TypeUtil.js\";\r\nimport { ObjectUtil } from \"../../../Utility/ObjectUtil.js\";\r\nimport { readToBuffer } from \"../../../Utility/StreamUtil.js\";\r\nimport { Dispatcher } from \"undici-types\";\r\nimport { IndexBatchOptions, ReplicationBatchOptions } from \"../../Session/IAdvancedSessionOperations.js\";\r\nimport { ShardedBatchOptions } from \"./ShardedBatchOptions.js\";\r\n\r\nexport class SingleNodeBatchCommand extends RavenCommand<BatchCommandResult> implements IDisposable {\r\n    private _supportsAtomicWrites: boolean | null;\r\n    private readonly _attachmentStreams: Set<AttachmentData>;\r\n    private readonly _conventions: DocumentConventions;\r\n    private readonly _commands: ICommandData[];\r\n    private readonly _options: BatchOptions;\r\n    private readonly _mode: TransactionMode;\r\n\r\n    public constructor(conventions: DocumentConventions, commands: ICommandData[]);\r\n    public constructor(conventions: DocumentConventions, commands: ICommandData[], options: BatchOptions);\r\n    public constructor(\r\n        conventions: DocumentConventions,\r\n        commands: ICommandData[],\r\n        options: BatchOptions,\r\n        transactionMode: TransactionMode);\r\n    public constructor(\r\n        conventions: DocumentConventions,\r\n        commands: ICommandData[],\r\n        options: BatchOptions = null,\r\n        mode: TransactionMode = null) {\r\n        super();\r\n        this._commands = commands;\r\n        this._conventions = conventions;\r\n        this._options = options;\r\n        this._mode = mode;\r\n\r\n        if (!conventions) {\r\n            throwError(\"InvalidArgumentException\", \"conventions cannot be null\");\r\n        }\r\n\r\n        if (!commands) {\r\n            throwError(\"InvalidArgumentException\", \"commands cannot be null\");\r\n        }\r\n\r\n        for (const command of this._commands) {\r\n            if (command instanceof PutAttachmentCommandData) {\r\n                const putAttachmentCommandData = command as PutAttachmentCommandData;\r\n                if (!this._attachmentStreams) {\r\n                    this._attachmentStreams = new Set();\r\n                }\r\n\r\n                const { attStream } = putAttachmentCommandData;\r\n                if (this._attachmentStreams.has(attStream)) {\r\n                    PutAttachmentCommandHelper.throwStreamWasAlreadyUsed();\r\n                } else {\r\n                    this._attachmentStreams.add(attStream);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    async send(agent: Dispatcher, requestOptions: HttpRequestParameters): Promise<{\r\n        response: HttpResponse;\r\n        bodyStream: Readable\r\n    }> {\r\n        const { body } = requestOptions;\r\n        if (body instanceof FormData) {\r\n            const attachments = [...this._attachmentStreams]\r\n                .map(attStream => {\r\n                    return {\r\n                        body: attStream,\r\n                        headers: {\r\n                            \"Command-Type\": \"AttachmentStream\"\r\n                        }\r\n                    };\r\n                });\r\n\r\n            for (let i = 0; i < attachments.length; i++) {\r\n                const part = attachments[i].body;\r\n                const payload = part instanceof Readable ? await readToBuffer(part) : part;\r\n                body.append(\"attachment_\" + i, payload);\r\n            }\r\n        }\r\n\r\n        return super.send(agent, requestOptions);\r\n    }\r\n\r\n    public createRequest(node: ServerNode): HttpRequestParameters {\r\n        const uri = node.url + \"/databases/\" + node.database + \"/bulk_docs?\";\r\n        const headers = HeadersBuilder.create().typeAppJson().build();\r\n\r\n        if (TypeUtil.isNullOrUndefined(this._supportsAtomicWrites)) {\r\n            this._supportsAtomicWrites = node.supportsAtomicClusterWrites;\r\n        }\r\n\r\n        const commandsArray = this._commands.map(x => {\r\n            const serialized = x.serialize(this._conventions);\r\n            if (!this._supportsAtomicWrites) {\r\n                delete serialized[\"OriginalChangeVector\"];\r\n            }\r\n\r\n            return serialized;\r\n        })\r\n\r\n        const body = JsonSerializer.getDefault().serialize({\r\n            Commands: commandsArray,\r\n            TransactionMode: this._mode === \"ClusterWide\" ? \"ClusterWide\" : undefined\r\n        });\r\n\r\n        const queryString = this._appendOptions();\r\n        const request: HttpRequestParameters = {\r\n            method: \"POST\",\r\n            uri: uri + queryString,\r\n        };\r\n\r\n        if (this._attachmentStreams && this._attachmentStreams.size > 0) {\r\n            // NOTE: payload is created in send method in async fashion - to support conversion from readable to buffers\r\n\r\n            // strip out content type, see: https://stackoverflow.com/questions/39280438/fetch-missing-boundary-in-multipart-form-data-post\r\n            if (request.headers && \"Content-Type\" in request.headers) {\r\n                const { \"Content-Type\": contentType, ...restHeaders } = request.headers;\r\n                request.headers = restHeaders;\r\n            }\r\n\r\n            const multipart = new FormData();\r\n            multipart.append(\"main\", new Blob([body], { type: \"application/json\" }));\r\n\r\n            request.body = multipart;\r\n\r\n        } else {\r\n            request.body = body;\r\n            request.headers = headers;\r\n        }\r\n\r\n        return request;\r\n    }\r\n\r\n    public async setResponseAsync(bodyStream: Stream, fromCache: boolean): Promise<string> {\r\n        if (!bodyStream) {\r\n            throwError(\"InvalidOperationException\",\r\n                \"Got null response from the server after doing a batch,\"\r\n                + \" something is very wrong. Probably a garbled response.\");\r\n        }\r\n\r\n        let body: string = null;\r\n        this.result = await RavenCommandResponsePipeline.create<BatchCommandResult>()\r\n            .collectBody(_ => body = _)\r\n            .parseJsonSync()\r\n            .objectKeysTransform({\r\n                defaultTransform: ObjectUtil.camel,\r\n                ignoreKeys: [/^@/],\r\n                ignorePaths: [/results\\.\\[\\]\\.modifiedDocument\\./i],\r\n            })\r\n            .process(bodyStream);\r\n        return body;\r\n    }\r\n\r\n    protected _appendOptions(): string {\r\n        if (!this._options) {\r\n            return \"\";\r\n        }\r\n\r\n        return SingleNodeBatchCommand.appendOptions(this._options.indexOptions, this._options.replicationOptions, this._options.shardedOptions);\r\n\r\n    }\r\n\r\n    protected static appendOptions(indexOptions: IndexBatchOptions, replicationOptions: ReplicationBatchOptions, shardedOptions: ShardedBatchOptions): string {\r\n        let result = \"\";\r\n\r\n        if (replicationOptions) {\r\n            result += `&waitForReplicasTimeout=${TimeUtil.millisToTimeSpan(replicationOptions.timeout)}`;\r\n\r\n            result += \"&throwOnTimeoutInWaitForReplicas=\" + (replicationOptions.throwOnTimeout ? \"true\" : \"false\");\r\n\r\n            result += \"&numberOfReplicasToWaitFor=\";\r\n            result += replicationOptions.majority ? \"majority\" : replicationOptions.replicas;\r\n        }\r\n\r\n        if (indexOptions) {\r\n            result += \"&waitForIndexesTimeout=\";\r\n            result += TimeUtil.millisToTimeSpan(indexOptions.timeout);\r\n\r\n            if (indexOptions.throwOnTimeout) {\r\n                result += \"&waitForIndexThrow=true\";\r\n            } else {\r\n                result += \"&waitForIndexThrow=false\";\r\n            }\r\n\r\n            if (indexOptions.indexes) {\r\n                for (const specificIndex of indexOptions.indexes) {\r\n                    result += \"&waitForSpecificIndex=\" + encodeURIComponent(specificIndex);\r\n                }\r\n            }\r\n        }\r\n\r\n        if (shardedOptions) {\r\n            if (shardedOptions.batchBehavior !== \"Default\") {\r\n                result += \"&shardedBatchBehavior=\" + shardedOptions.batchBehavior;\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    public get isReadRequest(): boolean {\r\n        return false;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\r\n    public dispose(): void {\r\n        // empty\r\n    }\r\n}\r\n"]}