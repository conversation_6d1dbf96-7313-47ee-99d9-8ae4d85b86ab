{"version": 3, "file": "IncrementalTimeSeriesBatchCommandData.js", "sourceRoot": "", "sources": ["../../../../../src/Documents/Commands/Batches/IncrementalTimeSeriesBatchCommandData.ts"], "names": [], "mappings": ";;;AACA,yEAAmE;AAInE,MAAa,qCAAsC,SAAQ,gDAAqB;IAE5E,YAAmB,UAAkB,EAAE,IAAY,EAAE,UAAgC;QACjF,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAExB,IAAI,UAAU,EAAE,CAAC;YACb,KAAK,MAAM,kBAAkB,IAAI,UAAU,EAAE,CAAC;gBAC1C,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YAClD,CAAC;QACL,CAAC;IACL,CAAC;IAED,IAAW,IAAI;QACX,OAAO,0BAA0B,CAAC;IACtC,CAAC;CACJ;AAfD,sFAeC", "sourcesContent": ["import { CommandType } from \"../CommandData.js\";\r\nimport { TimeSeriesCommandData } from \"./TimeSeriesCommandData.js\";\r\nimport { IncrementOperation } from \"../../Operations/TimeSeries/TimeSeriesOperation.js\";\r\n\r\n\r\nexport class IncrementalTimeSeriesBatchCommandData extends TimeSeriesCommandData {\r\n\r\n    public constructor(documentId: string, name: string, increments: IncrementOperation[]) {\r\n        super(documentId, name);\r\n\r\n        if (increments) {\r\n            for (const incrementOperation of increments) {\r\n                this.timeSeries.increment(incrementOperation);\r\n            }\r\n        }\r\n    }\r\n\r\n    public get type(): CommandType {\r\n        return \"TimeSeriesWithIncrements\";\r\n    }\r\n}\r\n"]}