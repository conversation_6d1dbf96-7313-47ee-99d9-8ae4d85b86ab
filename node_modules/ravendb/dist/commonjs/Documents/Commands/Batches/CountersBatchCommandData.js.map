{"version": 3, "file": "CountersBatchCommandData.js", "sourceRoot": "", "sources": ["../../../../../src/Documents/Commands/Batches/CountersBatchCommandData.ts"], "names": [], "mappings": ";;;AACA,kEAA4D;AAC5D,2DAA0D;AAE1D,yGAAmG;AAGnG,MAAa,wBAAwB;IAChB,GAAG,CAAS;IACZ,KAAK,CAAS;IACd,aAAa,CAAS;IAC/B,QAAQ,CAAU;IACT,SAAS,CAA4B;IAItD,YAAmB,UAAkB,EAAE,iBAAwD;QAC3F,IAAI,0BAAU,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5C,IAAA,qBAAU,EAAC,0BAA0B,EAAE,4BAA4B,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,IAAA,qBAAU,EAAC,0BAA0B,EAAE,8CAA8C,CAAC,CAAC;QAC3F,CAAC;QAED,IAAI,CAAC,GAAG,GAAG,UAAU,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,wDAAyB,EAAE,CAAC;QACjD,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,CAAC;QACvC,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC;YACxD,CAAC,CAAC,iBAAiB;YACnB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC;IAC9B,CAAC;IAED,IAAW,EAAE;QACT,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB,CAAC;IAED,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,IAAI;QACX,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,SAAS,CAAC,WAAmB;QAChC,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IACzD,CAAC;IAEM,YAAY,CAAC,WAAmB;QACnC,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IAC5D,CAAC;IAEO,iBAAiB,CAAC,IAA0B,EAAE,WAAmB;QACrE,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;YACzC,IAAI,WAAW,KAAK,EAAE,CAAC,WAAW,EAAE,CAAC;gBACjC,SAAS;YACb,CAAC;YAED,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACnB,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,SAAS;QACZ,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,GAAG;YACZ,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YACpC,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,IAAI,CAAC,QAAQ,IAAI,SAAS;SACtC,CAAC;IACN,CAAC;CACJ;AAlFD,4DAkFC", "sourcesContent": ["import { ICommandData, CommandType } from \"../CommandData.js\";\r\nimport { StringUtil } from \"../../../Utility/StringUtil.js\";\r\nimport { throwError } from \"../../../Exceptions/index.js\";\r\nimport { CounterOperationType } from \"../../Operations/Counters/CounterOperationType.js\";\r\nimport { DocumentCountersOperation } from \"../../Operations/Counters/DocumentCountersOperation.js\";\r\nimport { CounterOperation } from \"../../Operations/Counters/CounterOperation.js\";\r\n\r\nexport class CountersBatchCommandData implements ICommandData {\r\n    private readonly _id: string;\r\n    private readonly _name: string;\r\n    private readonly _changeVector: string;\r\n    private _fromEtl: boolean;\r\n    private readonly _counters: DocumentCountersOperation;\r\n\r\n    public constructor(documentId: string, counterOperation: CounterOperation);\r\n    public constructor(documentId: string, counterOperations: CounterOperation[]);\r\n    public constructor(documentId: string, counterOperations: CounterOperation | CounterOperation[]) {\r\n        if (StringUtil.isNullOrWhitespace(documentId)) {\r\n            throwError(\"InvalidArgumentException\", \"DocumentId cannot be null.\");\r\n        }\r\n\r\n        if (!counterOperations) {\r\n            throwError(\"InvalidArgumentException\", \"Argument 'counterOperations' cannot be null.\");\r\n        }\r\n\r\n        this._id = documentId;\r\n        this._name = null;\r\n        this._changeVector = null;\r\n        this._counters = new DocumentCountersOperation();\r\n        this._counters.documentId = documentId;\r\n        this._counters.operations = Array.isArray(counterOperations)\r\n            ? counterOperations\r\n            : [counterOperations];\r\n    }\r\n\r\n    public get id() {\r\n        return this._id;\r\n    }\r\n\r\n    public get name() {\r\n        return this._name;\r\n    }\r\n\r\n    public get changeVector() {\r\n        return this._changeVector;\r\n    }\r\n\r\n    public get fromEtl() {\r\n        return this._fromEtl;\r\n    }\r\n\r\n    public get counters() {\r\n        return this._counters;\r\n    }\r\n\r\n    public get type(): CommandType {\r\n        return \"Counters\";\r\n    }\r\n\r\n    public hasDelete(counterName: string): boolean {\r\n        return this._hasOperationType(\"Delete\", counterName);\r\n    }\r\n\r\n    public hasIncrement(counterName: string): boolean {\r\n        return this._hasOperationType(\"Increment\", counterName);\r\n    }\r\n\r\n    private _hasOperationType(type: CounterOperationType, counterName: string): boolean {\r\n        for (const op of this._counters.operations) {\r\n            if (counterName !== op.counterName) {\r\n                continue;\r\n            }\r\n\r\n            if (op.type === type) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public serialize(): object {\r\n        return {\r\n            Id: this._id,\r\n            Counters: this._counters.serialize(),\r\n            Type: \"Counters\",\r\n            FromEtl: this._fromEtl || undefined\r\n        };\r\n    }\r\n}\r\n"]}