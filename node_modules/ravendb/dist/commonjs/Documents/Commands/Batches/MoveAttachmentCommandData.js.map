{"version": 3, "file": "MoveAttachmentCommandData.js", "sourceRoot": "", "sources": ["../../../../../src/Documents/Commands/Batches/MoveAttachmentCommandData.ts"], "names": [], "mappings": ";;;AACA,kEAA4D;AAC5D,2DAA0D;AAG1D,MAAa,yBAAyB;IAC3B,EAAE,CAAS;IACX,YAAY,CAAS;IACrB,IAAI,CAAS;IACZ,cAAc,CAAS;IACvB,gBAAgB,CAAS;IAEjC,IAAW,IAAI;QACX,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED,YACI,UAAkB,EAClB,IAAY,EACZ,qBAA6B,EAC7B,eAAuB,EACvB,YAAoB;QACpB,IAAI,0BAAU,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5C,IAAA,qBAAU,EAAC,0BAA0B,EAAE,yBAAyB,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,0BAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,IAAA,qBAAU,EAAC,0BAA0B,EAAE,mBAAmB,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,0BAAU,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,EAAE,CAAC;YACvD,IAAA,qBAAU,EAAC,0BAA0B,EAAE,oCAAoC,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,0BAAU,CAAC,kBAAkB,CAAC,eAAe,CAAC,EAAE,CAAC;YACjD,IAAA,qBAAU,EAAC,0BAA0B,EAAE,8BAA8B,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,cAAc,GAAG,qBAAqB,CAAC;QAC5C,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;IAC5C,CAAC;IAEM,OAAO;QACV,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAEM,SAAS,CAAC,WAAgC;QAC7C,OAAO;YACH,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,IAAI,EAAE,gBAA+B;SACxC,CAAC;IACN,CAAC;CACJ;AAtDD,8DAsDC", "sourcesContent": ["import { ICommandData, CommandType } from \"../CommandData.js\";\r\nimport { StringUtil } from \"../../../Utility/StringUtil.js\";\r\nimport { throwError } from \"../../../Exceptions/index.js\";\r\nimport { DocumentConventions } from \"../../Conventions/DocumentConventions.js\";\r\n\r\nexport class MoveAttachmentCommandData implements ICommandData {\r\n    public id: string;\r\n    public changeVector: string;\r\n    public name: string;\r\n    private _destinationId: string;\r\n    private _destinationName: string;\r\n\r\n    public get type(): CommandType {\r\n        return \"AttachmentMOVE\";\r\n    }\r\n\r\n    public constructor(\r\n        documentId: string, \r\n        name: string, \r\n        destinationDocumentId: string, \r\n        destinationName: string, \r\n        changeVector: string) {\r\n        if (StringUtil.isNullOrWhitespace(documentId)) {\r\n            throwError(\"InvalidArgumentException\", \"DocumentId is required.\");\r\n        }\r\n\r\n        if (StringUtil.isNullOrWhitespace(name)) {\r\n            throwError(\"InvalidArgumentException\", \"Name is required.\");\r\n        }\r\n\r\n        if (StringUtil.isNullOrWhitespace(destinationDocumentId)) {\r\n            throwError(\"InvalidArgumentException\", \"DestinationDocumentId is required.\");\r\n        }\r\n\r\n        if (StringUtil.isNullOrWhitespace(destinationName)) {\r\n            throwError(\"InvalidArgumentException\", \"DestinationName is required.\");\r\n        }\r\n        \r\n        this.id = documentId;\r\n        this.name = name;\r\n        this.changeVector = changeVector;\r\n        this._destinationId = destinationDocumentId;\r\n        this._destinationName = destinationName;\r\n    }\r\n\r\n    public getType(): CommandType {\r\n        return \"AttachmentMOVE\";\r\n    }\r\n\r\n    public serialize(conventions: DocumentConventions): object {\r\n        return {\r\n            Id: this.id,\r\n            Name: this.name,\r\n            DestinationId: this._destinationId,\r\n            DestinationName: this._destinationName,\r\n            ChangeVector: this.changeVector,\r\n            Type: \"AttachmentMOVE\" as CommandType\r\n        };\r\n    }\r\n}\r\n"]}