"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeSeriesCommandData = void 0;
const TimeSeriesOperation_js_1 = require("../../Operations/TimeSeries/TimeSeriesOperation.js");
const index_js_1 = require("../../../Exceptions/index.js");
class TimeSeriesCommandData {
    _id;
    _name;
    _timeSeries;
    constructor(documentId, name) {
        if (!documentId) {
            (0, index_js_1.throwError)("InvalidArgumentException", "DocumentId cannot be null");
        }
        if (!name) {
            (0, index_js_1.throwError)("InvalidArgumentException", "Name cannot be null");
        }
        this._id = documentId;
        this._name = name;
        this._timeSeries = new TimeSeriesOperation_js_1.TimeSeriesOperation();
        this._timeSeries.name = name;
    }
    get id() {
        return this._id;
    }
    set id(value) {
        this._id = value;
    }
    get name() {
        return this._name;
    }
    set name(value) {
        this._name = value;
    }
    get changeVector() {
        return null;
    }
    get timeSeries() {
        return this._timeSeries;
    }
    serialize(conventions) {
        return {
            Id: this._id,
            TimeSeries: this._timeSeries.serialize(conventions),
            Type: "TimeSeries"
        };
    }
    onBeforeSaveChanges(session) {
        // empty by design
    }
}
exports.TimeSeriesCommandData = TimeSeriesCommandData;
//# sourceMappingURL=TimeSeriesCommandData.js.map