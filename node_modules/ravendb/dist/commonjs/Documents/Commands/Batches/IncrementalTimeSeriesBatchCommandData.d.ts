import { CommandType } from "../CommandData.js";
import { TimeSeriesCommandData } from "./TimeSeriesCommandData.js";
import { IncrementOperation } from "../../Operations/TimeSeries/TimeSeriesOperation.js";
export declare class IncrementalTimeSeriesBatchCommandData extends TimeSeriesCommandData {
    constructor(documentId: string, name: string, increments: IncrementOperation[]);
    get type(): CommandType;
}
//# sourceMappingURL=IncrementalTimeSeriesBatchCommandData.d.ts.map