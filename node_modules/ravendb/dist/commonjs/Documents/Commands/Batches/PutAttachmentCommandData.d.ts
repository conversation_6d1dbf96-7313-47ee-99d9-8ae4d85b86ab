import { ICommandData, CommandType } from "../CommandData.js";
import { AttachmentData } from "../../Attachments/index.js";
import { DocumentConventions } from "../../Conventions/DocumentConventions.js";
export declare class PutAttachmentCommandData implements ICommandData {
    id: string;
    name: string;
    changeVector: string;
    type: CommandType;
    contentType: string;
    attStream: AttachmentData;
    constructor(documentId: string, name: string, stream: AttachmentData, contentType: string, changeVector: string);
    serialize(conventions: DocumentConventions): object;
}
//# sourceMappingURL=PutAttachmentCommandData.d.ts.map