"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PutAttachmentCommandHelper = void 0;
const index_js_1 = require("../../../Exceptions/index.js");
class PutAttachmentCommandHelper {
    static throwStreamWasAlreadyUsed() {
        (0, index_js_1.throwError)("InvalidOperationException", "It is forbidden to re-use the same InputStream for more than one attachment. "
            + "Use a unique InputStream per put attachment command.");
    }
}
exports.PutAttachmentCommandHelper = PutAttachmentCommandHelper;
//# sourceMappingURL=PutAttachmentCommandHelper.js.map