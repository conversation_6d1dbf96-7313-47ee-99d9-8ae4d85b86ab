{"version": 3, "file": "BulkInsertOperationBase.js", "sourceRoot": "", "sources": ["../../../../src/Documents/BulkInsert/BulkInsertOperationBase.ts"], "names": [], "mappings": ";;;AAAA,wDAAuD;AAEvD,MAAsB,uBAAuB;IAEjC,cAAc,GAAY,KAAK,CAAC;IAC9B,sBAAsB,CAAe;IACrC,6BAA6B,GAAG,KAAK,CAAC;IACtC,YAAY,GAAG,CAAC,CAAC,CAAC;IAIlB,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxB,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAE3B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC/B,CAAC;QAED,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACrC,IAAI,CAAC;gBACD,MAAM,IAAI,CAAC,sBAAsB,CAAC;YACtC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC;IACL,CAAC;IAES,KAAK,CAAC,uBAAuB,CAAC,CAAQ,EAAE,UAAiB,IAAI;QACnE,IAAI,eAAsB,CAAC;QAC3B,IAAI,CAAC;YACD,eAAe,GAAG,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAC9D,CAAC;QAAC,MAAM,CAAC;YACL,iEAAiE;QACrE,CAAC;QACD,4BAA4B;QAE5B,IAAI,eAAe,EAAE,CAAC;YAClB,MAAM,eAAe,CAAC;QAC1B,CAAC;QAED,IAAA,qBAAU,EAAC,4BAA4B,EAAE,+BAA+B,EAAE,CAAC,CAAC,CAAC;IACjF,CAAC;CAOJ;AA/CD,0DA+CC", "sourcesContent": ["import { throwError } from \"../../Exceptions/index.js\";\r\n\r\nexport abstract class BulkInsertOperationBase<T> {\r\n\r\n    private _streamEnsured: boolean = false;\r\n    protected _bulkInsertExecuteTask: Promise<any>;\r\n    protected _bulkInsertExecuteTaskErrored = false;\r\n    protected _operationId = -1;\r\n\r\n    public abstract store(entity: T, id: string): Promise<void>;\r\n\r\n    protected async _executeBeforeStore() {\r\n        if (!this._streamEnsured) {\r\n            await this._waitForId();\r\n            await this._ensureStream();\r\n\r\n            this._streamEnsured = true;\r\n        }\r\n\r\n        if (this._bulkInsertExecuteTaskErrored) {\r\n            try {\r\n                await this._bulkInsertExecuteTask;\r\n            } catch (error) {\r\n                await this._throwBulkInsertAborted(error);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected async _throwBulkInsertAborted(e: Error, flushEx: Error = null) {\r\n        let errorFromServer: Error;\r\n        try {\r\n            errorFromServer = await this._getExceptionFromOperation();\r\n        } catch {\r\n            // server is probably down, will propagate the original exception\r\n        }\r\n        //TODO: use flushEx variable\r\n\r\n        if (errorFromServer) {\r\n            throw errorFromServer;\r\n        }\r\n\r\n        throwError(\"BulkInsertAbortedException\", \"Failed to execute bulk insert\", e);\r\n    }\r\n    protected abstract _waitForId(): Promise<void>;\r\n    protected abstract _ensureStream(): Promise<void>;\r\n\r\n    protected abstract _getExceptionFromOperation(): Promise<Error>;\r\n\r\n    public abstract abort(): Promise<void>;\r\n}\r\n"]}