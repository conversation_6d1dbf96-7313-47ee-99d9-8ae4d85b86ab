export declare abstract class BulkInsertOperationBase<T> {
    private _streamEnsured;
    protected _bulkInsertExecuteTask: Promise<any>;
    protected _bulkInsertExecuteTaskErrored: boolean;
    protected _operationId: number;
    abstract store(entity: T, id: string): Promise<void>;
    protected _executeBeforeStore(): Promise<void>;
    protected _throwBulkInsertAborted(e: Error, flushEx?: Error): Promise<void>;
    protected abstract _waitForId(): Promise<void>;
    protected abstract _ensureStream(): Promise<void>;
    protected abstract _getExceptionFromOperation(): Promise<Error>;
    abstract abort(): Promise<void>;
}
//# sourceMappingURL=BulkInsertOperationBase.d.ts.map