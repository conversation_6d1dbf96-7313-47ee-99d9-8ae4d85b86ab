{"version": 3, "file": "BulkInsertWriterBase.js", "sourceRoot": "", "sources": ["../../../../src/Documents/BulkInsert/BulkInsertWriterBase.ts"], "names": [], "mappings": ";;;AACA,6CAAqC;AACrC,6CAAiD;AAEjD,yCAAsC;AACtC,2DAAqD;AAGrD,MAAa,oBAAoB;IACZ,gBAAgB,GAAG,IAAI,GAAG,IAAI,CAAC;IAExC,WAAW,GAAkB,OAAO,CAAC,OAAO,EAAE,CAAC;IAC/C,eAAe,GAAY,IAAI,CAAC;IAC9B,cAAc,CAAmB;IACnC,iBAAiB,CAAmB;IACpC,eAAe,GAAY,IAAI,CAAC;IAEjC,iBAAiB,CAAO;IAExB,iBAAiB,CAAoB;IACrC,yBAAyB,GAAY,KAAK,CAAC;IAC3C,gBAAgB,CAAO;IAE9B;QACI,IAAI,CAAC,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAEjD,IAAI,CAAC,cAAc,GAAG,IAAI,gBAAgB,EAAE,CAAC;QAC7C,IAAI,CAAC,iBAAiB,GAAG,IAAI,gBAAgB,EAAE,CAAC;QAEhD,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,OAAO;QACT,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC9B,MAAM,IAAI,CAAC,WAAW,CAAC;gBAEvB,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACzD,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;YACzC,CAAC;QACL,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;QAC1C,CAAC;IACL,CAAC;IAEM,UAAU;QACb,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACtD,CAAC;IAEM,aAAa;QAChB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,eAAe,CAAC;IACtF,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,KAAK,GAAG,KAAK;QACpC,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;YACvB,MAAM,IAAI,CAAC,WAAW,CAAC;YAEvB,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC;YAChC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC;YAC7C,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAC;YAE7B,IAAI,CAAC,cAAc,GAAG,IAAI,gBAAgB,EAAE,CAAC;YAE7C,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;YACjD,KAAK,GAAG,IAAI,CAAC,CAAC,qIAAqI;YACnJ,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAEO,gBAAgB;QACpB,IAAI,CAAC,iBAAiB,GAAG,IAAI,IAAI,EAAE,CAAC;IACxC,CAAC;IAES,uBAAuB,CAAC,kBAAoC;QAClE,kBAAkB;IACtB,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,gBAAyB,KAAK;QACtE,IAAI,CAAC;YACD,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAErC,IAAI,aAAa,EAAE,CAAC;gBAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;YACzC,CAAC;YACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM,KAAK,GAAG,IAAA,qBAAS,EAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBACrD,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC5C,CAAC;QACL,CAAC;gBAAS,CAAC;YACP,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAChC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,WAAqC;QAC3D,IAAI,WAAW,KAAK,MAAM,EAAE,CAAC;YACzB,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,CAAC;YACjD,IAAI,CAAC,gBAAgB,GAAG,UAAU,EAAE,CAAC;YACrC,IAAA,sBAAQ,EAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,EAAE,sBAAQ,CAAC,IAAI,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC;CACJ;AArGD,oDAqGC;AAED,MAAa,gBAAgB;IAER,MAAM,GAA2B,EAAE,CAAC;IAC7C,WAAW,GAAG,CAAC,CAAC;IAEjB,IAAI,CAAC,IAAqB;QAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,WAAW,IAAI,oBAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACtF,CAAC;IAEM,QAAQ;QACX,MAAM,MAAM,GAAG,oBAAM,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpD,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACrC,IAAI,oBAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBAChC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;gBAC/B,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC;YAC/B,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;gBAChC,GAAG,IAAI,oBAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC3C,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;CACJ;AA7BD,4CA6BC;AAED,MAAa,iBAAkB,SAAQ,sBAAQ;IAC3C;QACI,KAAK,CAAC;YACF,aAAa,EAAE,IAAI,GAAG,IAAI;SAC7B,CAAC,CAAC;IACP,CAAC;IAEO,QAAQ,CAAgB;IACxB,OAAO,CAAa;IAE5B,KAAK,CAAC,IAAY;QACd,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,IAAqB;QACvB,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;gBAClC,IAAI,CAAC,OAAO,GAAG,GAAG,EAAE;oBAChB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;oBACpB,OAAO,EAAE,CAAC;gBACd,CAAC,CAAC;YACN,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED,KAAK,CAAC,KAAK;QACP,MAAM,IAAI,CAAC,QAAQ,CAAC;IACxB,CAAC;CACJ;AA7BD,8CA6BC", "sourcesContent": ["import { IDisposable } from \"../../Types/Contracts.js\";\r\nimport { Buffer } from \"node:buffer\";\r\nimport { pipeline, Readable } from \"node:stream\";\r\nimport type { Gzip } from \"node:zlib\";\r\nimport { promisify } from \"node:util\";\r\nimport { TypeUtil } from \"../../Utility/TypeUtil.js\";\r\nimport { HttpCompressionAlgorithm } from \"../../Http/HttpCompressionAlgorithm.js\";\r\n\r\nexport class BulkInsertWriterBase implements IDisposable {\r\n    private readonly _maxSizeInBuffer = 1024 * 1024;\r\n\r\n    private _asyncWrite: Promise<void> = Promise.resolve();\r\n    private _asyncWriteDone: boolean = true;\r\n    protected _currentWriter: BulkInsertStream;\r\n    private _backgroundWriter: BulkInsertStream;\r\n    private _isInitialWrite: boolean = true;\r\n\r\n    public lastFlushToStream: Date;\r\n\r\n    public requestBodyStream: RequestBodyStream;\r\n    public requestBodyStreamFinished: boolean = false;\r\n    public compressedStream: Gzip;\r\n\r\n    protected constructor() {\r\n        this.requestBodyStream = new RequestBodyStream();\r\n\r\n        this._currentWriter = new BulkInsertStream();\r\n        this._backgroundWriter = new BulkInsertStream();\r\n\r\n        this._updateFlushTime();\r\n    }\r\n\r\n    async dispose(): Promise<void> {\r\n        if (this.requestBodyStreamFinished) {\r\n            return;\r\n        }\r\n\r\n        try {\r\n            if (this.requestBodyStream) {\r\n                this._currentWriter.push(\"]\");\r\n                await this._asyncWrite;\r\n\r\n                await this.writeToStream(this._currentWriter.toBuffer());\r\n                await this.requestBodyStream.flush();\r\n            }\r\n        } finally {\r\n            this.requestBodyStreamFinished = true;\r\n        }\r\n    }\r\n\r\n    public initialize(): void {\r\n        this.onCurrentWriteStreamSet(this._currentWriter);\r\n    }\r\n\r\n    public isFlushNeeded() {\r\n        return this._currentWriter.length > this._maxSizeInBuffer || this._asyncWriteDone;\r\n    }\r\n\r\n    public async flushIfNeeded(force = false): Promise<void> {\r\n        if (this.isFlushNeeded()) {\r\n            await this._asyncWrite;\r\n\r\n            const tmp = this._currentWriter;\r\n            this._currentWriter = this._backgroundWriter;\r\n            this._backgroundWriter = tmp;\r\n\r\n            this._currentWriter = new BulkInsertStream();\r\n\r\n            const buffer = this._backgroundWriter.toBuffer();\r\n            force = true; // original version: force || this.isHeartbeatIntervalExceeded() || ; in node.js we need to force flush to use backpressure in steams\r\n            this._asyncWriteDone = false;\r\n            this._asyncWrite = this.writeToStream(buffer, force);\r\n        }\r\n    }\r\n\r\n    private _updateFlushTime(): void {\r\n        this.lastFlushToStream = new Date();\r\n    }\r\n\r\n    protected onCurrentWriteStreamSet(currentWriteStream: BulkInsertStream): void {\r\n        // empty by design\r\n    }\r\n\r\n    private async writeToStream(buffer: Buffer, forceDstFlush: boolean = false): Promise<void> {\r\n        try {\r\n            this.requestBodyStream.write(buffer);\r\n\r\n            if (forceDstFlush) {\r\n                this._updateFlushTime();\r\n                await this.requestBodyStream.flush();\r\n            }\r\n            if (this.compressedStream) {\r\n                const flush = promisify(this.compressedStream.flush);\r\n                await flush.call(this.compressedStream);\r\n            }\r\n        } finally {\r\n            this._asyncWriteDone = true;\r\n        }\r\n    }\r\n\r\n    public async ensureStream(compression: HttpCompressionAlgorithm) {\r\n        if (compression === \"Gzip\") {\r\n            const { createGzip } = await import(\"node:zlib\");\r\n            this.compressedStream = createGzip();\r\n            pipeline(this.requestBodyStream, this.compressedStream, TypeUtil.NOOP);\r\n        }\r\n\r\n        this._currentWriter.push(\"[\");\r\n    }\r\n}\r\n\r\nexport class BulkInsertStream {\r\n\r\n    private readonly _items: Array<string | Buffer> = [];\r\n    private totalLength = 0;\r\n\r\n    public push(data: string | Buffer) {\r\n        this._items.push(data);\r\n        this.totalLength += Buffer.isBuffer(data) ? data.length : Buffer.byteLength(data);\r\n    }\r\n\r\n    public toBuffer(): Buffer {\r\n        const result = Buffer.allocUnsafe(this.totalLength);\r\n        let idx = 0;\r\n        for (const inputElement of this._items) {\r\n            if (Buffer.isBuffer(inputElement)) {\r\n                inputElement.copy(result, idx);\r\n                idx += inputElement.length;\r\n            } else {\r\n                result.write(inputElement, idx);\r\n                idx += Buffer.byteLength(inputElement);\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    public get length() {\r\n        return this.totalLength;\r\n    }\r\n}\r\n\r\nexport class RequestBodyStream extends Readable {\r\n    constructor() {\r\n        super({\r\n            highWaterMark: 1024 * 1024\r\n        });\r\n    }\r\n\r\n    private _pending: Promise<void>;\r\n    private _resume: () => void;\r\n\r\n    _read(size: number) {\r\n        this._resume?.();\r\n    }\r\n\r\n    write(data: Buffer | string) {\r\n        const canConsumeMore = this.push(data);\r\n        if (!canConsumeMore) {\r\n            this._pending = new Promise(resolve => {\r\n                this._resume = () => {\r\n                    this._resume = null;\r\n                    resolve();\r\n                };\r\n            });\r\n        }\r\n    }\r\n\r\n    async flush(): Promise<void> {\r\n        await this._pending;\r\n    }\r\n}\r\n"]}