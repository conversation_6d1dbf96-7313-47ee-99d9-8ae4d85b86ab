{"version": 3, "file": "BulkInsertWriter.js", "sourceRoot": "", "sources": ["../../../../src/Documents/BulkInsert/BulkInsertWriter.ts"], "names": [], "mappings": ";;;AAAA,uEAAmF;AAEnF,wDAAuD;AAEvD,MAAa,gBAAiB,SAAQ,8CAAoB;IACtD;QACI,KAAK,EAAE,CAAC;IACZ,CAAC;IAES,uBAAuB,CAAC,kBAAoC;QAClE,QAAQ;IACZ,CAAC;IAED,sDAAsD;IAE/C,KAAK,CAAC,KAAsB;QAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAEO,MAAM,CAAC,0BAA0B;QACrC,IAAA,qBAAU,EAAC,2BAA2B,EAAE,iEAAiE,CAAC,CAAC;IAC/G,CAAC;CACJ;AAlBD,4CAkBC", "sourcesContent": ["import { BulkInsertStream, BulkInsertWriterBase } from \"./BulkInsertWriterBase.js\";\r\nimport { Buffer } from \"node:buffer\";\r\nimport { throwError } from \"../../Exceptions/index.js\";\r\n\r\nexport class BulkInsertWriter extends BulkInsertWriterBase {\r\n    public constructor() {\r\n        super();\r\n    }\r\n\r\n    protected onCurrentWriteStreamSet(currentWriteStream: BulkInsertStream) {\r\n        // empty\r\n    }\r\n\r\n    // no need for flushIfNeeded -> it uses parent version\r\n\r\n    public write(value: string | Buffer) {\r\n        this._currentWriter.push(value);\r\n    }\r\n\r\n    private static throwUnexpectedWriteStream() {\r\n        throwError(\"InvalidOperationException\", \"We got stream for which we don't have the stream writer defined\");\r\n    }\r\n}\r\n"]}