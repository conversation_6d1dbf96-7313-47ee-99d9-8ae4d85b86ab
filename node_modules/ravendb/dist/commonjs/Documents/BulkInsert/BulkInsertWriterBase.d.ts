import { IDisposable } from "../../Types/Contracts.js";
import { Buffer } from "node:buffer";
import { Readable } from "node:stream";
import type { Gzip } from "node:zlib";
import { HttpCompressionAlgorithm } from "../../Http/HttpCompressionAlgorithm.js";
export declare class BulkInsertWriterBase implements IDisposable {
    private readonly _maxSizeInBuffer;
    private _asyncWrite;
    private _asyncWriteDone;
    protected _currentWriter: BulkInsertStream;
    private _backgroundWriter;
    private _isInitialWrite;
    lastFlushToStream: Date;
    requestBodyStream: RequestBodyStream;
    requestBodyStreamFinished: boolean;
    compressedStream: Gzip;
    protected constructor();
    dispose(): Promise<void>;
    initialize(): void;
    isFlushNeeded(): boolean;
    flushIfNeeded(force?: boolean): Promise<void>;
    private _updateFlushTime;
    protected onCurrentWriteStreamSet(currentWriteStream: BulkInsertStream): void;
    private writeToStream;
    ensureStream(compression: HttpCompressionAlgorithm): Promise<void>;
}
export declare class BulkInsertStream {
    private readonly _items;
    private totalLength;
    push(data: string | Buffer): void;
    toBuffer(): Buffer;
    get length(): number;
}
export declare class RequestBodyStream extends Readable {
    constructor();
    private _pending;
    private _resume;
    _read(size: number): void;
    write(data: Buffer | string): void;
    flush(): Promise<void>;
}
//# sourceMappingURL=BulkInsertWriterBase.d.ts.map