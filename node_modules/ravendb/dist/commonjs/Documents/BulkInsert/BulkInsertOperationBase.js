"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulkInsertOperationBase = void 0;
const index_js_1 = require("../../Exceptions/index.js");
class BulkInsertOperationBase {
    _streamEnsured = false;
    _bulkInsertExecuteTask;
    _bulkInsertExecuteTaskErrored = false;
    _operationId = -1;
    async _executeBeforeStore() {
        if (!this._streamEnsured) {
            await this._waitForId();
            await this._ensureStream();
            this._streamEnsured = true;
        }
        if (this._bulkInsertExecuteTaskErrored) {
            try {
                await this._bulkInsertExecuteTask;
            }
            catch (error) {
                await this._throwBulkInsertAborted(error);
            }
        }
    }
    async _throwBulkInsertAborted(e, flushEx = null) {
        let errorFromServer;
        try {
            errorFromServer = await this._getExceptionFromOperation();
        }
        catch {
            // server is probably down, will propagate the original exception
        }
        //TODO: use flushEx variable
        if (errorFromServer) {
            throw errorFromServer;
        }
        (0, index_js_1.throwError)("BulkInsertAbortedException", "Failed to execute bulk insert", e);
    }
}
exports.BulkInsertOperationBase = BulkInsertOperationBase;
//# sourceMappingURL=BulkInsertOperationBase.js.map