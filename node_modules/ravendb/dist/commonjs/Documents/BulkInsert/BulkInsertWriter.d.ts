import { BulkInsertStream, BulkInsertWriterBase } from "./BulkInsertWriterBase.js";
import { Buffer } from "node:buffer";
export declare class BulkInsertWriter extends BulkInsertWriterBase {
    constructor();
    protected onCurrentWriteStreamSet(currentWriteStream: BulkInsertStream): void;
    write(value: string | Buffer): void;
    private static throwUnexpectedWriteStream;
}
//# sourceMappingURL=BulkInsertWriter.d.ts.map