import { Readable } from "node:stream";
import { HttpResponse } from "../../Primitives/Http.js";
import { CapitalizeType } from "../../Types/index.js";
export type AttachmentType = "Document" | "Revision";
export interface AttachmentName {
    name: string;
    hash: string;
    contentType: string;
    size: number;
}
export interface AttachmentNameWithCount extends AttachmentName {
    count: number;
}
export interface IAttachmentObject extends CapitalizeType<AttachmentName> {
    getContentAsString(): string;
    getContentAsString(encoding: string): string;
    getContentAsStream(): any;
}
export interface AttachmentDetails extends AttachmentName {
    changeVector: string;
    documentId?: string;
}
export declare class AttachmentResult {
    data: Readable;
    details: AttachmentDetails;
    private _response;
    constructor(data: Readable, details: AttachmentDetails, _response: HttpResponse);
    dispose(): void;
}
export type AttachmentData = Readable | Buffer;
//# sourceMappingURL=index.d.ts.map