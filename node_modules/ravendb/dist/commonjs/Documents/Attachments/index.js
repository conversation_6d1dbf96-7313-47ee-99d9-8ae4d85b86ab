"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttachmentResult = void 0;
const HttpUtil_js_1 = require("../../Utility/HttpUtil.js");
class AttachmentResult {
    data;
    details;
    _response;
    constructor(data, details, _response) {
        this.data = data;
        this.details = details;
        this._response = _response;
        // empty
    }
    dispose() {
        return (0, HttpUtil_js_1.closeHttpResponse)(this._response);
    }
}
exports.AttachmentResult = AttachmentResult;
//# sourceMappingURL=index.js.map