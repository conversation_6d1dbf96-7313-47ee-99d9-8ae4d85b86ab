{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/Documents/Attachments/index.ts"], "names": [], "mappings": ";;;AAEA,2DAA8D;AA2B9D,MAAa,gBAAgB;IAGd;IACA;IACC;IAHZ,YACW,IAAc,EACd,OAA0B,EACzB,SAAuB;QAFxB,SAAI,GAAJ,IAAI,CAAU;QACd,YAAO,GAAP,OAAO,CAAmB;QACzB,cAAS,GAAT,SAAS,CAAc;QAC/B,QAAQ;IACZ,CAAC;IAEM,OAAO;QACV,OAAO,IAAA,+BAAiB,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC7C,CAAC;CACJ;AAZD,4CAYC", "sourcesContent": ["import { Readable } from \"node:stream\";\r\nimport { HttpResponse } from \"../../Primitives/Http.js\";\r\nimport { closeHttpResponse } from \"../../Utility/HttpUtil.js\";\r\nimport { CapitalizeType } from \"../../Types/index.js\";\r\n\r\nexport type AttachmentType = \"Document\" | \"Revision\";\r\n\r\nexport interface AttachmentName {\r\n    name: string;\r\n    hash: string;\r\n    contentType: string;\r\n    size: number;\r\n}\r\n\r\nexport interface AttachmentNameWithCount extends AttachmentName {\r\n    count: number;\r\n}\r\n\r\nexport interface IAttachmentObject extends CapitalizeType<AttachmentName> {\r\n    getContentAsString(): string;\r\n    getContentAsString(encoding: string): string;\r\n    getContentAsStream(): any;\r\n}\r\n\r\nexport interface AttachmentDetails extends AttachmentName {\r\n    changeVector: string;\r\n    documentId?: string;\r\n}\r\n\r\nexport class AttachmentResult {\r\n\r\n    constructor(\r\n        public data: Readable,\r\n        public details: AttachmentDetails,\r\n        private _response: HttpResponse) {\r\n        // empty\r\n    }\r\n\r\n    public dispose() {\r\n        return closeHttpResponse(this._response);\r\n    }\r\n}\r\n\r\nexport type AttachmentData = Readable | Buffer;\r\n"]}