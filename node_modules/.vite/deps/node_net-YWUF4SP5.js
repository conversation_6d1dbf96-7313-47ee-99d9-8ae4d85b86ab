import {
  __commonJS
} from "./chunk-DC5AMYBS.js";

// browser-external:node:net
var require_node_net = __commonJS({
  "browser-external:node:net"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:net" has been externalized for browser compatibility. Cannot access "node:net.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});
export default require_node_net();
//# sourceMappingURL=node_net-YWUF4SP5.js.map
