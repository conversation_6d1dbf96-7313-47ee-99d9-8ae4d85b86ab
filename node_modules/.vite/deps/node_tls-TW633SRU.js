import {
  __commonJS
} from "./chunk-DC5AMYBS.js";

// browser-external:node:tls
var require_node_tls = __commonJS({
  "browser-external:node:tls"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:tls" has been externalized for browser compatibility. Cannot access "node:tls.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});
export default require_node_tls();
//# sourceMappingURL=node_tls-TW633SRU.js.map
