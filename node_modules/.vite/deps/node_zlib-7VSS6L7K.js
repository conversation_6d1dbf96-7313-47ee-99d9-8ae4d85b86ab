import {
  __commonJS
} from "./chunk-DC5AMYBS.js";

// browser-external:node:zlib
var require_node_zlib = __commonJS({
  "browser-external:node:zlib"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:zlib" has been externalized for browser compatibility. Cannot access "node:zlib.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});
export default require_node_zlib();
//# sourceMappingURL=node_zlib-7VSS6L7K.js.map
