# RavenDB Setup Instructions

## Current Status
The application is currently using a **Mock RavenDB Service** for demonstration purposes. This allows you to see the UI and functionality without needing a real RavenDB server.

## Switching to Real RavenDB

### Step 1: Set up RavenDB Server

#### Option A: Local RavenDB Server
1. Download RavenDB from [https://ravendb.net/download](https://ravendb.net/download)
2. Extract and run the server:
   ```bash
   # On Windows
   ./Server/Raven.Server.exe
   
   # On Linux/Mac
   ./Server/Raven.Server
   ```
3. Access RavenDB Studio at `http://localhost:8080`
4. Create a database named `SampleDatabase`

#### Option B: RavenDB Cloud
1. Sign up at [https://cloud.ravendb.net](https://cloud.ravendb.net)
2. Create a database and note the connection details
3. Update the configuration with your cloud database URL

### Step 2: Update the Application Code

Replace the mock service with the real RavenDB service in `src/App.tsx`:

**Current (Mock):**
```typescript
import { MockRavenDBService, type SampleDocument } from "./services/mockRavendb";

// In the component:
const ravenService = new MockRavenDBService();
```

**Change to (Real RavenDB):**
```typescript
import { RavenDBService, type SampleDocument } from "./services/ravendb";

// In the component:
const ravenService = new RavenDBService();
```

### Step 3: Configure Connection (Optional)

Create a `.env` file in the project root:
```bash
cp .env.example .env
```

Edit `.env` with your RavenDB settings:
```
VITE_RAVENDB_URL=http://localhost:8080
VITE_RAVENDB_DATABASE=SampleDatabase
```

### Step 4: Handle Browser Compatibility Issues

**Important:** The RavenDB Node.js client library may have compatibility issues in browser environments. For production applications, consider:

1. **Backend API Approach (Recommended):**
   - Create a backend API (Express.js, Fastify, etc.) that uses RavenDB
   - Have your React app call the backend API instead of connecting directly to RavenDB
   - This is the recommended approach for production applications

2. **Browser-Compatible Alternative:**
   - Use RavenDB's REST API directly with fetch/axios
   - Implement custom HTTP client for RavenDB operations

### Example Backend API Structure

If you choose the backend API approach, create a simple Express.js server:

```javascript
// server.js
const express = require('express');
const { DocumentStore } = require('ravendb');

const app = express();
const store = new DocumentStore(['http://localhost:8080'], 'SampleDatabase');
store.initialize();

app.get('/api/documents', async (req, res) => {
  const session = store.openSession();
  try {
    const documents = await session.query({ collection: 'SampleDocuments' }).all();
    res.json(documents);
  } finally {
    session.dispose();
  }
});

app.listen(3001, () => {
  console.log('API server running on port 3001');
});
```

Then update your React app to call `http://localhost:3001/api/documents` instead of using the RavenDB client directly.

## Troubleshooting

### Common Issues

1. **Import Errors in Browser:**
   - RavenDB client is designed for Node.js
   - Solution: Use backend API approach

2. **CORS Issues:**
   - Configure RavenDB server CORS settings
   - Or use backend API as proxy

3. **Connection Timeouts:**
   - Check RavenDB server is running
   - Verify network connectivity
   - Check firewall settings

### Testing the Mock Service

The current mock service provides:
- ✅ Connection status simulation
- ✅ Sample data creation
- ✅ Document listing
- ✅ Simulated network delays
- ✅ Error handling demonstration

This allows you to develop and test the UI without needing a real RavenDB server.
