export interface SampleDocument {
  id?: string
  name: string
  description: string
  createdAt: string
}

// Mock data storage (in a real app, this would be in RavenDB)
let mockDocuments: SampleDocument[] = []
let nextId = 1

export class MockRavenDBService {
  private isConnected = true

  constructor() {
    // Initialize with some sample data if empty
    if (mockDocuments.length === 0) {
      this.initializeSampleData()
    }
  }

  private initializeSampleData() {
    mockDocuments = [
      {
        id: `documents/${nextId++}-A`,
        name: 'Welcome Document',
        description: 'This is a sample document created to demonstrate the RavenDB integration.',
        createdAt: new Date().toISOString()
      }
    ]
  }

  async testConnection(): Promise<boolean> {
    // Simulate network delay
    await this.delay(500)
    return this.isConnected
  }

  async getDocuments(): Promise<SampleDocument[]> {
    // Simulate network delay
    await this.delay(300)
    
    if (!this.isConnected) {
      throw new Error('Not connected to database')
    }
    
    return [...mockDocuments] // Return a copy
  }

  async createSampleData(): Promise<void> {
    // Simulate network delay
    await this.delay(800)
    
    if (!this.isConnected) {
      throw new Error('Not connected to database')
    }

    const sampleDocuments: SampleDocument[] = [
      {
        id: `documents/${nextId++}-A`,
        name: 'Sample Document 1',
        description: 'This is the first sample document created for testing.',
        createdAt: new Date().toISOString()
      },
      {
        id: `documents/${nextId++}-A`,
        name: 'Sample Document 2',
        description: 'This is the second sample document with different content.',
        createdAt: new Date().toISOString()
      },
      {
        id: `documents/${nextId++}-A`,
        name: 'Sample Document 3',
        description: 'This is the third sample document to demonstrate data fetching.',
        createdAt: new Date().toISOString()
      }
    ]

    // Add to mock storage
    mockDocuments.push(...sampleDocuments)
  }

  async createDocument(document: Omit<SampleDocument, 'id' | 'createdAt'>): Promise<string> {
    // Simulate network delay
    await this.delay(400)
    
    if (!this.isConnected) {
      throw new Error('Not connected to database')
    }

    const newDocument: SampleDocument = {
      id: `documents/${nextId++}-A`,
      ...document,
      createdAt: new Date().toISOString()
    }

    mockDocuments.push(newDocument)
    return newDocument.id!
  }

  async deleteDocument(id: string): Promise<void> {
    // Simulate network delay
    await this.delay(300)
    
    if (!this.isConnected) {
      throw new Error('Not connected to database')
    }

    const index = mockDocuments.findIndex(doc => doc.id === id)
    if (index > -1) {
      mockDocuments.splice(index, 1)
    }
  }

  // Simulate connection issues for testing
  setConnectionStatus(connected: boolean) {
    this.isConnected = connected
  }

  // Clear all mock data
  clearData() {
    mockDocuments = []
  }

  dispose() {
    // Nothing to dispose in mock implementation
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}
